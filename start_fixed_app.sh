#!/bin/bash

# Enhanced startup script with fixes for countdown and image issues

echo "🚀 Starting Enhanced Waiting List App (Fixed Version)"
echo "====================================================="
echo ""

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to check if a port is in use
check_port() {
    lsof -ti:$1 > /dev/null 2>&1
}

# Function to start backend
start_backend() {
    echo -e "${BLUE}📡 Starting Laravel Backend...${NC}"
    cd backend
    
    # Check if port 8000 is already in use
    if check_port 8000; then
        echo -e "${GREEN}✅ Backend already running on port 8000${NC}"
    else
        echo "🔧 Running migrations..."
        php artisan migrate --force
        
        echo "📊 Updating waiting counts..."
        php artisan waiting:update-counts
        
        echo "🌐 Starting Laravel server..."
        php artisan serve --host=0.0.0.0 --port=8000 &
        BACKEND_PID=$!
        echo -e "${GREEN}✅ Backend started with PID: $BACKEND_PID${NC}"
        
        # Wait for server to start
        sleep 3
        
        # Test if server is responding
        response=$(curl -s -w "%{http_code}" -o /dev/null http://***********:8000/api/restaurants/public)
        if [ "$response" = "200" ]; then
            echo -e "${GREEN}✅ Backend API is responding correctly${NC}"
        else
            echo -e "${RED}❌ Backend API not responding (HTTP $response)${NC}"
        fi
    fi
    
    cd ..
}

# Function to start frontend
start_frontend() {
    echo ""
    echo -e "${BLUE}📱 Starting React Native Frontend...${NC}"
    cd frontend
    
    # Check if port 8081 is already in use
    if check_port 8081; then
        echo -e "${GREEN}✅ Frontend already running on port 8081${NC}"
    else
        echo "📦 Installing dependencies..."
        npm install --silent
        
        echo "🚀 Starting Expo development server..."
        npx expo start &
        FRONTEND_PID=$!
        echo -e "${GREEN}✅ Frontend started with PID: $FRONTEND_PID${NC}"
    fi
    
    cd ..
}

# Function to show connection info
show_info() {
    echo ""
    echo -e "${GREEN}🎉 Enhanced App is ready with fixes!${NC}"
    echo ""
    echo -e "${YELLOW}📡 Backend API: http://***********:8000/api${NC}"
    echo -e "${YELLOW}📱 Frontend: http://localhost:8081${NC}"
    echo ""
    echo -e "${BLUE}🔧 Fixed Issues:${NC}"
    echo "  ✅ Countdown timer now properly decrements (3→2→1→0)"
    echo "  ✅ Profile images load correctly with proper URL construction"
    echo "  ✅ Interval cleanup prevents memory leaks"
    echo "  ✅ Enhanced error handling for image loading"
    echo ""
    echo -e "${BLUE}📋 Available API endpoints:${NC}"
    echo "  • GET  /api/restaurants/public - Public restaurant list with images"
    echo "  • POST /api/restaurant-users/{id}/mark-dine-in - Mark as dine-in"
    echo "  • POST /api/restaurant-users/{id}/mark-waiting - Mark as waiting"
    echo "  • GET  /api/restaurant-users/waiting-count - Get waiting count"
    echo ""
    echo -e "${BLUE}🧪 To test the fixes:${NC}"
    echo "  1. Open the app on your device/emulator"
    echo "  2. Navigate to waiting list"
    echo "  3. Click dine-in checkbox and watch countdown: 'Undo (3)' → 'Undo (2)' → 'Undo (1)'"
    echo "  4. Either click Undo or wait for auto-execution"
    echo "  5. Check restaurant list for profile images"
    echo ""
    echo -e "${BLUE}🔍 Debugging:${NC}"
    echo "  • Check browser console for 'Image URL constructed' logs"
    echo "  • Verify countdown updates every second"
    echo "  • Test with multiple users simultaneously"
    echo ""
    echo -e "${BLUE}📱 To connect your device:${NC}"
    echo "  1. Install Expo Go app on your phone"
    echo "  2. Scan the QR code that appears"
    echo "  3. Or press 'a' for Android emulator, 'i' for iOS simulator"
    echo ""
    echo -e "${BLUE}⚡ Enhanced Features:${NC}"
    echo "  • Aggregated waiting counts (sum of party sizes)"
    echo "  • 3-second countdown with visual feedback"
    echo "  • Profile image display with smart URL handling"
    echo "  • Real-time updates every 5 seconds"
    echo "  • Offline support with sync when reconnected"
    echo "  • Cross-device synchronization"
    echo ""
    echo -e "${BLUE}🛑 To stop the servers:${NC}"
    echo "  Press Ctrl+C or run: pkill -f 'php artisan serve'"
    echo ""
    echo -e "${GREEN}🎊 Ready to test the enhanced waiting list system!${NC}"
}

# Function to test fixes
test_fixes() {
    echo ""
    echo -e "${YELLOW}🧪 Running quick fix validation...${NC}"
    
    # Test countdown logic
    if grep -q "countdownTimers.*useRef" frontend/src/components/Table.tsx; then
        echo -e "${GREEN}✅ Countdown timer fix implemented${NC}"
    else
        echo -e "${RED}❌ Countdown timer fix missing${NC}"
    fi
    
    # Test image URL logic
    if grep -q "getImageUri.*storage" frontend/src/screens/RestaurantListScreen.tsx; then
        echo -e "${GREEN}✅ Image URL fix implemented${NC}"
    else
        echo -e "${RED}❌ Image URL fix missing${NC}"
    fi
    
    # Test API response
    if check_port 8000; then
        response=$(curl -s http://***********:8000/api/restaurants/public | grep -o '"current_waiting_count"' | head -1)
        if [ ! -z "$response" ]; then
            echo -e "${GREEN}✅ Backend API working with aggregated counts${NC}"
        else
            echo -e "${RED}❌ Backend API not responding correctly${NC}"
        fi
    fi
}

# Main execution
echo "Starting enhanced app with countdown and image fixes..."
start_backend
start_frontend
test_fixes
show_info

# Keep script running
echo -e "${YELLOW}Press Ctrl+C to stop all servers...${NC}"
wait
