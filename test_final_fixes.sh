#!/bin/bash

# Final test script for profile images and real-time updates

echo "🔧 Testing Profile Images & Real-time Updates"
echo "============================================="
echo ""

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print test results
print_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

# Function to print test section
print_section() {
    echo -e "${YELLOW}📋 $1${NC}"
    echo "----------------------------------------"
}

# Test 1: Profile Image URL Construction
print_section "Testing Profile Image URLs"

echo "🔍 Testing image URL construction logic..."
if grep -q "baseUrl.*replace.*api" ../frontend/src/screens/RestaurantListScreen.tsx; then
    print_result 0 "Enhanced URL construction logic implemented"
else
    print_result 1 "URL construction logic missing"
fi

if grep -q "console.log.*Image URL constructed" ../frontend/src/screens/RestaurantListScreen.tsx; then
    print_result 0 "Debug logging for image URLs added"
else
    print_result 1 "Debug logging missing"
fi

echo "🔍 Testing actual image accessibility..."
# Get a sample profile URL
profile_url=$(curl -s http://192.168.1.6:8000/api/restaurants/public | grep -o '"profile":"[^"]*"' | head -1 | sed 's/"profile":"//g' | sed 's/"//g')

if [ ! -z "$profile_url" ]; then
    echo "   Found profile URL: $profile_url"
    
    # Test the constructed URL
    if [[ "$profile_url" == /storage/* ]]; then
        full_url="http://192.168.1.6:8000${profile_url}"
        response=$(curl -s -w "%{http_code}" -o /dev/null "$full_url")
        
        if [ "$response" = "200" ]; then
            print_result 0 "Profile image is accessible at: $full_url"
        else
            print_result 1 "Profile image not accessible (HTTP $response)"
        fi
    else
        print_result 0 "Profile URL format detected: $profile_url"
    fi
else
    echo "⚠️  No profile images found in restaurants"
fi

echo ""

# Test 2: Real-time Updates
print_section "Testing Real-time Updates"

echo "🔍 Testing restaurant list real-time updates..."
if grep -q "setInterval.*fetchRestaurants" ../frontend/src/screens/RestaurantListScreen.tsx; then
    print_result 0 "Restaurant list real-time updates implemented (5-second interval)"
else
    print_result 1 "Restaurant list real-time updates missing"
fi

echo "🔍 Testing waiting list real-time updates..."
if grep -q "setInterval.*refresh" ../frontend/src/screens/DashboardScreen.tsx; then
    print_result 0 "Waiting list real-time updates implemented (5-second interval)"
else
    print_result 1 "Waiting list real-time updates missing"
fi

echo "🔍 Testing sync trigger on status change..."
if grep -q "syncService.syncData" ../frontend/src/hooks/useOfflineData.ts; then
    print_result 0 "Immediate sync on status change implemented"
else
    print_result 1 "Immediate sync on status change missing"
fi

echo ""

# Test 3: API Response Time
print_section "Testing API Performance"

echo "🔍 Testing API response time..."
start_time=$(date +%s%N)
response=$(curl -s -w "%{http_code}" -o /tmp/api_response.json http://192.168.1.6:8000/api/restaurants/public)
end_time=$(date +%s%N)
response_time=$(( (end_time - start_time) / 1000000 )) # Convert to milliseconds

if [ "$response" = "200" ]; then
    print_result 0 "API responding in ${response_time}ms"
    
    # Check if aggregated counts are present
    count_check=$(cat /tmp/api_response.json | grep -o '"current_waiting_count":[0-9]*' | head -3)
    if [ ! -z "$count_check" ]; then
        print_result 0 "Aggregated waiting counts present"
        echo "   Sample counts: $count_check"
    else
        print_result 1 "Aggregated waiting counts missing"
    fi
else
    print_result 1 "API not responding (HTTP $response)"
fi

echo ""

# Test 4: Frontend Build
print_section "Testing Frontend Build"

echo "🔍 Testing frontend compilation..."
cd ../frontend
build_result=$(npx expo export --platform android --quiet 2>&1)
if [ $? -eq 0 ]; then
    print_result 0 "Frontend builds successfully with all fixes"
else
    print_result 1 "Frontend build failed"
    echo "   Error: $build_result"
fi

echo ""

# Test 5: Manual Testing Guide
print_section "Manual Testing Instructions"

echo -e "${BLUE}🖼️  Testing Profile Images:${NC}"
echo "   1. Start app: npx expo start"
echo "   2. Go to restaurant list page"
echo "   3. Look for restaurants with profile images"
echo "   4. Check browser console for 'Image URL constructed' logs"
echo "   5. Verify images load or show fallback initials"
echo ""

echo -e "${BLUE}⚡ Testing Real-time Updates:${NC}"
echo "   1. Open app on two devices/browsers"
echo "   2. On device 1: Add a user to waiting list"
echo "   3. On device 2: Watch restaurant list count update within 5 seconds"
echo "   4. On device 1: Mark user as dine-in"
echo "   5. On device 2: Watch count decrease within 5 seconds"
echo "   6. Check waiting list updates every 5 seconds"
echo ""

echo -e "${BLUE}🔧 Debugging Tips:${NC}"
echo "   • Open browser console to see image URL logs"
echo "   • Watch network tab for API calls every 5 seconds"
echo "   • Test with multiple restaurants and users"
echo "   • Verify counts update immediately after status changes"
echo ""

# Summary
print_section "Fix Summary"

echo -e "${GREEN}🔧 Profile Image Fixes:${NC}"
echo "   ✅ Enhanced URL construction for /storage/ paths"
echo "   ✅ Proper base URL handling without /api suffix"
echo "   ✅ Debug logging for troubleshooting"
echo "   ✅ Fallback handling for missing images"
echo ""

echo -e "${GREEN}⚡ Real-time Update Fixes:${NC}"
echo "   ✅ Restaurant list updates every 5 seconds (all users)"
echo "   ✅ Waiting list updates every 5 seconds (logged users)"
echo "   ✅ Immediate sync on status changes"
echo "   ✅ Cross-device synchronization"
echo ""

echo -e "${GREEN}🎯 Expected Behavior:${NC}"
echo "   • Profile images load with correct URLs"
echo "   • Restaurant counts update automatically"
echo "   • Waiting list refreshes every 5 seconds"
echo "   • Status changes sync immediately"
echo "   • Works for both logged and non-logged users"
echo ""

echo -e "${BLUE}🚀 Ready to test! Start the app and verify both fixes work properly.${NC}"
