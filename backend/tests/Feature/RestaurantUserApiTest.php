<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\RestaurantUser;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Laravel\Sanctum\Sanctum;
use Tests\TestCase;

class RestaurantUserApiTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    /**
     * Test creating a restaurant user.
     */
    public function test_can_create_restaurant_user(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        $data = [
            'username' => $this->faker->name,
            'mobile_number' => '1234567890',
            'total_users_count' => 5,
        ];

        $response = $this->postJson('/api/restaurant-users', $data);

        $response->assertStatus(201)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'id',
                        'username',
                        'mobile_number',
                        'total_users_count',
                        'added_by',
                        'created_at',
                        'updated_at',
                    ],
                    'message'
                ]);

        $this->assertDatabaseHas('restaurant_users', [
            'username' => $data['username'],
            'mobile_number' => $data['mobile_number'],
            'added_by' => $user->id,
        ]);
    }

    /**
     * Test getting restaurant users list.
     */
    public function test_can_get_restaurant_users_list(): void
    {
        $user = User::factory()->create();
        Sanctum::actingAs($user);

        RestaurantUser::factory()->count(3)->create(['added_by' => $user->id]);

        $response = $this->getJson('/api/restaurant-users');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        '*' => [
                            'id',
                            'username',
                            'mobile_number',
                            'total_users_count',
                            'added_by',
                            'created_at',
                            'updated_at',
                        ]
                    ],
                    'meta' => [
                        'current_page',
                        'last_page',
                        'per_page',
                        'total',
                    ],
                    'message'
                ]);
    }

    /**
     * Test authentication is required.
     */
    public function test_authentication_required(): void
    {
        $response = $this->getJson('/api/restaurant-users');
        $response->assertStatus(401);
    }
}
