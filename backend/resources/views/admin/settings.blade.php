@extends('admin.layout')

@section('title', 'Settings')

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Application Settings</h1>
        <p class="mt-2 text-gray-600">Manage application configuration and branding</p>
    </div>

    <!-- Settings Form -->
    <div class="bg-white shadow overflow-hidden sm:rounded-lg card-shadow">
        <form method="POST" action="{{ route('admin.settings.update') }}" enctype="multipart/form-data">
            @csrf
            
            <div class="px-4 py-5 sm:p-6">
                <div class="grid grid-cols-1 gap-6">
                    
                    <!-- Application Name -->
                    <div>
                        <label for="application_name" class="block text-sm font-medium text-gray-700">
                            Application Name
                        </label>
                        <div class="mt-1">
                            <input type="text" 
                                   name="application_name" 
                                   id="application_name" 
                                   value="{{ old('application_name', $settings->application_name) }}"
                                   class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                                   required>
                        </div>
                        @error('application_name')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- App Version -->
                    <div>
                        <label for="app_version" class="block text-sm font-medium text-gray-700">
                            App Version
                        </label>
                        <div class="mt-1">
                            <input type="text" 
                                   name="app_version" 
                                   id="app_version" 
                                   value="{{ old('app_version', $settings->app_version) }}"
                                   class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md"
                                   required>
                        </div>
                        @error('app_version')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Logo Upload -->
                    <div>
                        <label for="logo" class="block text-sm font-medium text-gray-700">
                            Logo
                        </label>
                        <div class="mt-1 flex items-center space-x-4">
                            <div class="flex-shrink-0">
                                @if($settings->logo)
                                    <img id="logo-preview" 
                                         src="{{ Storage::url($settings->logo) }}" 
                                         alt="Current Logo" 
                                         class="h-20 w-20 object-cover rounded-lg border border-gray-300">
                                @else
                                    <div id="logo-preview" 
                                         class="h-20 w-20 bg-gray-200 rounded-lg border border-gray-300 flex items-center justify-center">
                                        <span class="text-gray-400 text-xs">No Logo</span>
                                    </div>
                                @endif
                            </div>
                            <div class="flex-1">
                                <input type="file" 
                                       name="logo" 
                                       id="logo" 
                                       accept="image/*"
                                       class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100"
                                       onchange="previewImage(this, 'logo-preview')">
                                <p class="mt-1 text-xs text-gray-500">PNG, JPG, GIF up to 2MB</p>
                            </div>
                        </div>
                        @error('logo')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Favicon Upload -->
                    <div>
                        <label for="favicon" class="block text-sm font-medium text-gray-700">
                            Favicon
                        </label>
                        <div class="mt-1 flex items-center space-x-4">
                            <div class="flex-shrink-0">
                                @if($settings->favicon)
                                    <img id="favicon-preview" 
                                         src="{{ Storage::url($settings->favicon) }}" 
                                         alt="Current Favicon" 
                                         class="h-16 w-16 object-cover rounded border border-gray-300">
                                @else
                                    <div id="favicon-preview" 
                                         class="h-16 w-16 bg-gray-200 rounded border border-gray-300 flex items-center justify-center">
                                        <span class="text-gray-400 text-xs">No Favicon</span>
                                    </div>
                                @endif
                            </div>
                            <div class="flex-1">
                                <input type="file" 
                                       name="favicon" 
                                       id="favicon" 
                                       accept="image/*"
                                       class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100"
                                       onchange="previewImage(this, 'favicon-preview')">
                                <p class="mt-1 text-xs text-gray-500">PNG, JPG, GIF, ICO up to 1MB</p>
                            </div>
                        </div>
                        @error('favicon')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- App Logo Upload -->
                    <div>
                        <label for="app_logo" class="block text-sm font-medium text-gray-700">
                            Application Logo
                        </label>
                        <div class="mt-1 flex items-center space-x-4">
                            <div class="flex-shrink-0">
                                @if($settings->app_logo)
                                    <img id="app-logo-preview" 
                                         src="{{ Storage::url($settings->app_logo) }}" 
                                         alt="Current App Logo" 
                                         class="h-20 w-20 object-cover rounded-lg border border-gray-300">
                                @else
                                    <div id="app-logo-preview" 
                                         class="h-20 w-20 bg-gray-200 rounded-lg border border-gray-300 flex items-center justify-center">
                                        <span class="text-gray-400 text-xs">No App Logo</span>
                                    </div>
                                @endif
                            </div>
                            <div class="flex-1">
                                <input type="file" 
                                       name="app_logo" 
                                       id="app_logo" 
                                       accept="image/*"
                                       class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-indigo-50 file:text-indigo-700 hover:file:bg-indigo-100"
                                       onchange="previewImage(this, 'app-logo-preview')">
                                <p class="mt-1 text-xs text-gray-500">PNG, JPG, GIF up to 2MB</p>
                            </div>
                        </div>
                        @error('app_logo')
                            <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                </div>
            </div>

            <!-- Form Actions -->
            <div class="px-4 py-3 bg-gray-50 text-right sm:px-6">
                <button type="submit" 
                        class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Save Settings
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function previewImage(input, previewId) {
    const preview = document.getElementById(previewId);
    const file = input.files[0];
    
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.innerHTML = `<img src="${e.target.result}" alt="Preview" class="h-full w-full object-cover rounded">`;
        };
        reader.readAsDataURL(file);
    }
}
</script>
@endsection
