<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Web\AdminController;

// Redirect root to admin login
Route::get('/', [AdminController::class, 'showLogin'])->name('admin.login');

// Admin routes
Route::prefix('admin')->name('admin.')->group(function () {
    Route::get('/login', [AdminController::class, 'showLogin'])->name('login');
    Route::post('/login', [AdminController::class, 'login']);
    Route::post('/logout', [AdminController::class, 'logout'])->name('logout');

    Route::middleware('auth')->group(function () {
        Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard');
        Route::get('/restaurant-users/{owner}', [AdminController::class, 'showRestaurantUsers'])->name('restaurant-users');

        // Settings routes
        Route::get('/settings', [AdminController::class, 'settings'])->name('settings');
        Route::post('/settings', [AdminController::class, 'updateSettings'])->name('settings.update');

        // Restaurant routes
        Route::get('/restaurants', [AdminController::class, 'restaurants'])->name('restaurants');
        Route::get('/restaurants/create', [AdminController::class, 'createRestaurant'])->name('restaurants.create');
        Route::post('/restaurants', [AdminController::class, 'storeRestaurant'])->name('restaurants.store');
        Route::post('/restaurants/{id}/toggle-status', [AdminController::class, 'toggleRestaurantStatus'])->name('restaurants.toggle-status');
    });
});

// Add a login route to prevent authentication redirect errors for API
Route::get('/login', function () {
    return response()->json(['message' => 'Unauthenticated'], 401);
})->name('login');
