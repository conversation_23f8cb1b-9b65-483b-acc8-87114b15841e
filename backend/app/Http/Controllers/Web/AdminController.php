<?php

namespace App\Http\Controllers\Web;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\RestaurantUser;
use App\Models\Settings;
use App\Models\Restaurant;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\ValidationException;

class AdminController extends Controller
{
    /**
     * Show the admin login form
     */
    public function showLogin()
    {
        if (Auth::check() && Auth::user()->is_admin) {
            return redirect()->route('admin.dashboard');
        }

        return view('admin.login');
    }

    /**
     * Handle admin login
     */
    public function login(Request $request)
    {
        $request->validate([
            'email' => 'required|email',
            'password' => 'required|string',
        ]);

        $user = User::where('email', $request->email)->first();

        if (!$user || !Hash::check($request->password, $user->password) || !$user->is_admin) {
            throw ValidationException::withMessages([
                'email' => ['The provided credentials are incorrect or you are not an admin.'],
            ]);
        }

        Auth::login($user);

        return redirect()->route('admin.dashboard');
    }

    /**
     * Show admin dashboard
     */
    public function dashboard()
    {
        if (!Auth::check() || !Auth::user()->is_admin) {
            return redirect()->route('admin.login');
        }

        // Get dashboard statistics
        $stats = [
            'total_restaurant_owners' => User::whereHas('restaurantUsers')->count(),
            'total_restaurant_users' => RestaurantUser::count(),
            'total_users' => User::count(),
            'recent_registrations' => User::where('created_at', '>=', now()->subDays(7))->count(),
        ];

        // Get restaurant owners with their user counts
        $restaurantOwners = User::whereHas('restaurantUsers')
            ->withCount('restaurantUsers')
            ->with(['restaurantUsers' => function ($query) {
                $query->latest()->take(5);
            }])
            ->orderBy('restaurant_users_count', 'desc')
            ->paginate(10);

        return view('admin.dashboard', compact('stats', 'restaurantOwners'));
    }

    /**
     * Show restaurant users for a specific owner
     */
    public function showRestaurantUsers($ownerId)
    {
        if (!Auth::check() || !Auth::user()->is_admin) {
            return redirect()->route('admin.login');
        }

        $owner = User::findOrFail($ownerId);

        $restaurantUsers = RestaurantUser::where('added_by', $ownerId)
            ->with('addedBy')
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return view('admin.restaurant-users', compact('owner', 'restaurantUsers'));
    }

    /**
     * Show settings page
     */
    public function settings()
    {
        if (!Auth::check() || !Auth::user()->is_admin) {
            return redirect()->route('admin.login');
        }

        $settings = Settings::getInstance();
        return view('admin.settings', compact('settings'));
    }

    /**
     * Update settings
     */
    public function updateSettings(Request $request)
    {
        if (!Auth::check() || !Auth::user()->is_admin) {
            return redirect()->route('admin.login');
        }

        $request->validate([
            'application_name' => 'required|string|max:255',
            'app_version' => 'required|string|max:50',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'favicon' => 'nullable|image|mimes:jpeg,png,jpg,gif,ico|max:1024',
            'app_logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ]);

        $settings = Settings::getInstance();
        $updateData = [
            'application_name' => $request->application_name,
            'app_version' => $request->app_version,
        ];

        // Handle file uploads
        foreach (['logo', 'favicon', 'app_logo'] as $field) {
            if ($request->hasFile($field)) {
                // Delete old file if exists
                if ($settings->$field && Storage::disk('public')->exists($settings->$field)) {
                    Storage::disk('public')->delete($settings->$field);
                }

                // Store new file
                $path = $request->file($field)->store('settings', 'public');
                $updateData[$field] = $path;
            }
        }

        $settings->update($updateData);

        return redirect()->route('admin.settings')->with('success', 'Settings updated successfully!');
    }

    /**
     * Show restaurants page
     */
    public function restaurants(Request $request)
    {
        if (!Auth::check() || !Auth::user()->is_admin) {
            return redirect()->route('admin.login');
        }

        $query = Restaurant::with('owner');

        // Add search functionality
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->search($search);
        }

        // Add filter by status
        if ($request->has('status')) {
            $status = $request->get('status');
            if ($status === 'active') {
                $query->where('is_active', true);
            } elseif ($status === 'inactive') {
                $query->where('is_active', false);
            }
        }

        $restaurants = $query->orderBy('created_at', 'desc')->paginate(15);

        return view('admin.restaurants', compact('restaurants'));
    }

    /**
     * Show create restaurant form
     */
    public function createRestaurant()
    {
        if (!Auth::check() || !Auth::user()->is_admin) {
            return redirect()->route('admin.login');
        }

        $users = User::whereHas('restaurantUsers')->orWhere('is_admin', false)->get();
        return view('admin.create-restaurant', compact('users'));
    }

    /**
     * Store new restaurant
     */
    public function storeRestaurant(Request $request)
    {
        if (!Auth::check() || !Auth::user()->is_admin) {
            return redirect()->route('admin.login');
        }

        $request->validate([
            'name' => 'required|string|max:255',
            'contact_number' => 'required|string|max:20',
            'location' => 'nullable|string',
            'address_line_1' => 'nullable|string|max:255',
            'address_line_2' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'country' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'latitude' => 'nullable|numeric|between:-90,90',
            'longitude' => 'nullable|numeric|between:-180,180',
            'owner_id' => 'required|exists:users,id',
            'owner_name' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'profile' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_active' => 'boolean',
        ]);

        $data = $request->only([
            'name', 'contact_number', 'location',
            'address_line_1', 'address_line_2', 'city', 'state', 'country', 'postal_code',
            'latitude', 'longitude', 'owner_id', 'owner_name', 'description'
        ]);
        $data['is_active'] = $request->has('is_active');

        // Handle profile image upload
        if ($request->hasFile('profile')) {
            $path = $request->file('profile')->store('restaurants', 'public');
            $data['profile'] = $path;
        }

        Restaurant::create($data);

        return redirect()->route('admin.restaurants')->with('success', 'Restaurant created successfully!');
    }

    /**
     * Toggle restaurant status
     */
    public function toggleRestaurantStatus($id)
    {
        if (!Auth::check() || !Auth::user()->is_admin) {
            return redirect()->route('admin.login');
        }

        $restaurant = Restaurant::findOrFail($id);
        $restaurant->update(['is_active' => !$restaurant->is_active]);

        $status = $restaurant->is_active ? 'activated' : 'deactivated';
        return redirect()->route('admin.restaurants')->with('success', "Restaurant {$status} successfully!");
    }

    /**
     * Logout admin
     */
    public function logout()
    {
        Auth::logout();
        return redirect()->route('admin.login');
    }
}
