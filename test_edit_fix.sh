#!/bin/bash

# Test script to verify the edit user fix

echo "🔧 Testing Edit User Fix - Restaurant Count Update"
echo "================================================="
echo ""

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print test results
print_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

# Function to print test section
print_section() {
    echo -e "${YELLOW}📋 $1${NC}"
    echo "----------------------------------------"
}

# Test 1: Check if backend fix is applied
print_section "Backend Fix Verification"

# Check if update method now calls updateRestaurantWaitingCount
if grep -A 10 "public function update" ../backend/app/Http/Controllers/Api/RestaurantUserController.php | grep -q "updateRestaurantWaitingCount"; then
    print_result 0 "Update method now calls updateRestaurantWaitingCount"
else
    print_result 1 "Update method missing updateRestaurantWaitingCount call"
fi

# Check if destroy method now calls updateRestaurantWaitingCount
if grep -A 10 "public function destroy" ../backend/app/Http/Controllers/Api/RestaurantUserController.php | grep -q "updateRestaurantWaitingCount"; then
    print_result 0 "Destroy method now calls updateRestaurantWaitingCount"
else
    print_result 1 "Destroy method missing updateRestaurantWaitingCount call"
fi

echo ""

# Test 2: Check backend server
print_section "Backend Server Status"

response=$(curl -s -w "%{http_code}" -o /tmp/backend_test.json http://***********:8000/api/restaurants/public)
if [ "$response" = "200" ]; then
    print_result 0 "Backend server is running"
    
    # Get initial restaurant count
    initial_count=$(cat /tmp/backend_test.json | grep -o '"current_waiting_count":[0-9]*' | head -1 | grep -o '[0-9]*')
    echo "   Initial restaurant waiting count: $initial_count"
else
    print_result 1 "Backend server not responding"
    echo "Please start backend: cd backend && php artisan serve --host=0.0.0.0 --port=8000"
    exit 1
fi

echo ""

# Test 3: Frontend debugging status
print_section "Frontend Debugging Status"

# Check if debugging code is still in place
if grep -q "Updating user with full object" ../frontend/src/hooks/useOfflineData.ts; then
    print_result 0 "Frontend debugging code in place"
else
    print_result 1 "Frontend debugging code missing"
fi

# Check if sync triggers are in place
sync_count=$(grep -c "syncService.syncData" ../frontend/src/hooks/useOfflineData.ts)
if [ "$sync_count" -ge 8 ]; then
    print_result 0 "Frontend sync triggers in place ($sync_count found)"
else
    print_result 1 "Frontend sync triggers missing (only $sync_count found)"
fi

echo ""

# Test 4: Manual testing instructions
print_section "Manual Testing Instructions"

echo -e "${BLUE}🧪 To test the edit fix:${NC}"
echo ""
echo "1. Start the system:"
echo "   ./start_clean_system.sh"
echo ""
echo "2. Add a new user to waiting list:"
echo "   • Name: Test User"
echo "   • Mobile: 1234567890"
echo "   • Party Size: 4"
echo ""
echo "3. Note the restaurant's waiting count (should increase by 4)"
echo ""
echo "4. Edit the user you just added:"
echo "   • Change party size from 4 to 8"
echo "   • Save the changes"
echo ""
echo "5. Check restaurant list:"
echo "   • Count should increase by 4 more (from +4 to +8 total)"
echo "   • Update should happen within 5-10 seconds"
echo ""
echo -e "${BLUE}🔍 What to verify:${NC}"
echo ""
echo "✅ Expected behavior:"
echo "   • Add user: Restaurant count increases by party size"
echo "   • Edit user: Restaurant count updates to reflect new party size"
echo "   • Delete user: Restaurant count decreases by party size"
echo "   • All changes sync across devices within 5-10 seconds"
echo ""
echo "❌ Previous behavior:"
echo "   • Add user: Count increased ✅"
echo "   • Edit user: Count did NOT update ❌"
echo "   • Delete user: Count decreased ✅"
echo ""
echo -e "${BLUE}🔧 Technical fix applied:${NC}"
echo ""
echo "Backend Controller Changes:"
echo "• update() method: Added updateRestaurantWaitingCount() call"
echo "• destroy() method: Added updateRestaurantWaitingCount() call"
echo ""
echo "This ensures that when a user is edited or deleted, the restaurant's"
echo "aggregated waiting count is recalculated and updated immediately."
echo ""

# Test 5: API endpoint testing
print_section "API Endpoint Testing"

echo "🔍 Testing if we can get user data for editing..."

# Try to get users list
users_response=$(curl -s "http://***********:8000/api/restaurant-users?per_page=1" -H "Accept: application/json")
if echo "$users_response" | grep -q '"success":true'; then
    print_result 0 "Restaurant users API endpoint working"
    
    # Extract user ID for testing
    user_id=$(echo "$users_response" | grep -o '"id":[0-9]*' | head -1 | grep -o '[0-9]*')
    if [ ! -z "$user_id" ]; then
        echo "   Sample user ID for testing: $user_id"
    fi
else
    print_result 1 "Restaurant users API endpoint not working"
fi

echo ""

# Summary
print_section "Fix Summary"

echo -e "${GREEN}🎯 Root Cause Found and Fixed:${NC}"
echo ""
echo "❌ Problem: RestaurantUserController update() and destroy() methods"
echo "   were missing calls to updateRestaurantWaitingCount()"
echo ""
echo "✅ Solution: Added updateRestaurantWaitingCount() calls to both methods"
echo ""
echo -e "${BLUE}Before Fix:${NC}"
echo "• create() → ✅ Updates restaurant count"
echo "• update() → ❌ Does NOT update restaurant count"
echo "• destroy() → ❌ Does NOT update restaurant count"
echo "• markAsDineIn() → ✅ Updates restaurant count"
echo "• markAsWaiting() → ✅ Updates restaurant count"
echo ""
echo -e "${BLUE}After Fix:${NC}"
echo "• create() → ✅ Updates restaurant count"
echo "• update() → ✅ Updates restaurant count"
echo "• destroy() → ✅ Updates restaurant count"
echo "• markAsDineIn() → ✅ Updates restaurant count"
echo "• markAsWaiting() → ✅ Updates restaurant count"
echo ""
echo -e "${GREEN}🎊 Edit functionality should now work perfectly!${NC}"
echo ""
echo "Start testing with: ./start_clean_system.sh"
