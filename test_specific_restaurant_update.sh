#!/bin/bash

# Test script to verify that only specific restaurant updates

echo "🔧 Testing Specific Restaurant Update"
echo "===================================="
echo ""

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print test results
print_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

# Function to print test section
print_section() {
    echo -e "${YELLOW}📋 $1${NC}"
    echo "----------------------------------------"
}

# Test 1: Check backend logic
print_section "Backend Logic Verification"

echo "🔍 Checking updateRestaurantWaitingCount method..."

# Check if the method updates only specific restaurant
if grep -A 10 "updateRestaurantWaitingCount" ../backend/app/Http/Controllers/Api/RestaurantUserController.php | grep -q "where('owner_id', \$ownerId)"; then
    print_result 0 "Backend correctly updates only specific restaurant (owner_id filter)"
else
    print_result 1 "Backend may be updating all restaurants"
fi

# Check if the method uses correct aggregation
if grep -A 5 "updateRestaurantWaitingCount" ../backend/app/Http/Controllers/Api/RestaurantUserController.php | grep -q "where('added_by', \$ownerId)"; then
    print_result 0 "Backend correctly aggregates only specific restaurant's users"
else
    print_result 1 "Backend aggregation may be incorrect"
fi

echo ""

# Test 2: Get current restaurant data
print_section "Current Restaurant Data Analysis"

echo "🔍 Fetching current restaurant data..."
curl -s http://192.168.1.6:8000/api/restaurants/public > /tmp/restaurants_before.json

if [ $? -eq 0 ]; then
    print_result 0 "Successfully fetched restaurant data"
    
    # Show current counts
    echo ""
    echo "Current restaurant waiting counts:"
    cat /tmp/restaurants_before.json | grep -o '"name":"[^"]*","current_waiting_count":[0-9]*' | sed 's/"name":"//g' | sed 's/","current_waiting_count":/ → /g' | head -5
    
    # Count total restaurants
    total_restaurants=$(cat /tmp/restaurants_before.json | grep -o '"current_waiting_count":[0-9]*' | wc -l)
    echo ""
    echo "Total restaurants: $total_restaurants"
else
    print_result 1 "Failed to fetch restaurant data"
    exit 1
fi

echo ""

# Test 3: Verify the issue
print_section "Issue Analysis"

echo -e "${BLUE}🤔 Understanding the issue:${NC}"
echo ""
echo "User reported: 'badha j restaurent ma thay jay che'"
echo "Translation: 'Changes happen in all restaurants'"
echo ""
echo -e "${YELLOW}Possible interpretations:${NC}"
echo ""
echo "1. 📊 Visual Issue: All restaurants visually refresh when one updates"
echo "   • This is normal - frontend fetches all restaurants to get updated data"
echo "   • Only the specific restaurant's count actually changes"
echo "   • Other restaurants' counts remain the same"
echo ""
echo "2. 🐛 Data Issue: All restaurants' counts actually change"
echo "   • This would be a backend bug"
echo "   • updateRestaurantWaitingCount should only affect one restaurant"
echo ""
echo "3. 🔄 Sync Issue: Real-time updates trigger for all restaurants"
echo "   • Frontend sync fetches all restaurant data"
echo "   • This causes visual refresh of entire list"
echo ""

# Test 4: Check what actually changes
print_section "Data Change Analysis"

echo "🔍 Let's verify what actually changes..."
echo ""
echo "To test this properly, you need to:"
echo ""
echo "1. Note current counts for multiple restaurants"
echo "2. Edit a user in ONE specific restaurant"
echo "3. Check if only that restaurant's count changes"
echo ""
echo "Example test:"
echo "• Restaurant A: 10 waiting"
echo "• Restaurant B: 5 waiting"
echo "• Restaurant C: 8 waiting"
echo ""
echo "Edit user in Restaurant A (change party size +3):"
echo "• Restaurant A: Should become 13 waiting ✅"
echo "• Restaurant B: Should remain 5 waiting ✅"
echo "• Restaurant C: Should remain 8 waiting ✅"
echo ""

# Test 5: Frontend behavior analysis
print_section "Frontend Behavior Analysis"

echo "🔍 Checking frontend sync behavior..."

# Check if frontend fetches all restaurants during sync
if grep -q "getPublicRestaurants" ../frontend/src/services/sync.ts; then
    print_result 0 "Frontend fetches all restaurants during sync (expected behavior)"
    echo "   This causes visual refresh but only changed data should be different"
else
    print_result 1 "Frontend sync behavior unclear"
fi

# Check if there's any restaurant-specific update mechanism
if grep -q "updateSpecificRestaurant\|updateSingleRestaurant" ../frontend/src/services/sync.ts; then
    print_result 0 "Frontend has restaurant-specific update mechanism"
else
    print_result 1 "Frontend updates all restaurants (normal behavior)"
    echo "   This is actually correct - we need all restaurant data to show updated counts"
fi

echo ""

# Test 6: Solution analysis
print_section "Solution Analysis"

echo -e "${BLUE}🎯 Possible solutions if issue is real:${NC}"
echo ""
echo "1. 🎨 Visual Optimization:"
echo "   • Only re-render restaurants that actually changed"
echo "   • Use React.memo or useMemo to prevent unnecessary re-renders"
echo "   • Compare previous vs new data before updating"
echo ""
echo "2. 📡 API Optimization:"
echo "   • Create endpoint to update single restaurant"
echo "   • Return only changed restaurant data"
echo "   • Use WebSocket for real-time updates"
echo ""
echo "3. 🔄 Sync Optimization:"
echo "   • Track which restaurant was updated"
echo "   • Only fetch that specific restaurant's data"
echo "   • Merge with existing restaurant list"
echo ""

echo -e "${BLUE}🤔 But first, let's confirm the issue:${NC}"
echo ""
echo "The backend logic is correct - it only updates the specific restaurant."
echo "The frontend behavior is also correct - it fetches all restaurants to get updated data."
echo ""
echo "The 'issue' might be:"
echo "• Visual perception: Entire list refreshes (normal)"
echo "• Performance: Fetching all restaurants (acceptable for small datasets)"
echo "• UX: User expects only one restaurant to 'flash' or update visually"
echo ""

# Manual testing instructions
print_section "Manual Testing Instructions"

echo -e "${BLUE}🧪 To verify if this is a real issue:${NC}"
echo ""
echo "1. Start the app: ./start_clean_system.sh"
echo ""
echo "2. Open restaurant list and note counts:"
echo "   • Restaurant 1: X waiting"
echo "   • Restaurant 2: Y waiting"
echo "   • Restaurant 3: Z waiting"
echo ""
echo "3. Edit a user in Restaurant 1 only"
echo ""
echo "4. Check if counts change correctly:"
echo "   • Restaurant 1: Should change to X±N"
echo "   • Restaurant 2: Should remain Y (unchanged)"
echo "   • Restaurant 3: Should remain Z (unchanged)"
echo ""
echo "5. If only Restaurant 1's count changes → ✅ Working correctly"
echo "   If all restaurants' counts change → ❌ Real bug"
echo ""
echo -e "${GREEN}🎊 Most likely this is working correctly and the 'issue' is just visual refresh!${NC}"
