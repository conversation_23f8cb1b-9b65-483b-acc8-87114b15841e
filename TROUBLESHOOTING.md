# 🔧 Troubleshooting Guide - Waiting List App

## ❌ Network Error: API Connection Issues

### Problem
```
ERROR API Error: Network Error {"data": undefined, "method": "get", "status": undefined, "url": "/restaurants/public"}
```

### ✅ Solution Steps

1. **Start the Backend Server**
   ```bash
   cd backend
   php artisan serve --host=0.0.0.0 --port=8000
   ```

2. **Verify Backend is Running**
   ```bash
   curl http://***********:8000/api/restaurants/public
   ```
   Should return JSON with restaurant data.

3. **Check IP Address**
   ```bash
   ifconfig | grep "inet " | grep -v 127.0.0.1
   ```
   Update `frontend/src/utils/platformConfig.ts` if IP changed.

4. **Run Migrations**
   ```bash
   cd backend
   php artisan migrate
   php artisan waiting:update-counts
   ```

## 🚀 Quick Start (Automated)

```bash
./start_app.sh
```

## 📱 Manual Start

### Backend
```bash
cd backend
php artisan migrate
php artisan serve --host=0.0.0.0 --port=8000
```

### Frontend
```bash
cd frontend
npm install
npx expo start
```

## 🔍 Common Issues

### 1. Port Already in Use
```bash
# Kill existing processes
pkill -f "php artisan serve"
pkill -f "expo start"

# Or find and kill specific PIDs
lsof -ti:8000 | xargs kill -9
lsof -ti:8081 | xargs kill -9
```

### 2. Database Issues
```bash
cd backend
php artisan migrate:fresh --seed
php artisan waiting:update-counts
```

### 3. Frontend Bundle Errors
```bash
cd frontend
rm -rf node_modules
npm install
npx expo start --clear
```

### 4. IP Address Changed
Update these files with your current IP:
- `frontend/src/utils/platformConfig.ts`
- Lines 31-32: Update the IP addresses

### 5. Real-time Updates Not Working
- Ensure backend is running on correct IP
- Check network connectivity
- Verify user is logged in (real-time only works for authenticated users)

## 📊 Testing the Features

### 1. Test API Endpoints
```bash
# Get restaurants
curl http://***********:8000/api/restaurants/public

# Test with authentication (replace TOKEN)
curl -H "Authorization: Bearer TOKEN" http://***********:8000/api/restaurant-users

# Mark user as dine-in
curl -X POST -H "Authorization: Bearer TOKEN" http://***********:8000/api/restaurant-users/1/mark-dine-in
```

### 2. Test Real-time Features
1. Open app on multiple devices
2. Add a user to waiting list
3. Mark user as dine-in on one device
4. Watch count update on other devices within 5 seconds

### 3. Test Offline Functionality
1. Disconnect internet
2. Make status changes
3. Reconnect internet
4. Changes should sync automatically

## 🌐 Network Configuration

### For Android Emulator
- Use: `http://********:8000/api`

### For Physical Devices
- Use: `http://***********:8000/api` (your actual IP)

### For iOS Simulator
- Use: `http://***********:8000/api`

## 📝 Logs and Debugging

### Backend Logs
```bash
cd backend
tail -f storage/logs/laravel.log
```

### Frontend Logs
- Check Expo DevTools console
- Use React Native Debugger
- Check Metro bundler output

## 🆘 Still Having Issues?

1. **Check firewall settings** - Ensure ports 8000 and 8081 are open
2. **Verify network connectivity** - Both devices should be on same network
3. **Clear caches** - Clear Expo cache and Laravel cache
4. **Restart everything** - Restart backend, frontend, and devices

## 📞 Quick Commands Reference

```bash
# Start everything
./start_app.sh

# Stop everything
pkill -f "php artisan serve"
pkill -f "expo start"

# Reset database
cd backend && php artisan migrate:fresh --seed

# Clear frontend cache
cd frontend && npx expo start --clear

# Check what's running on ports
lsof -i :8000
lsof -i :8081
```
