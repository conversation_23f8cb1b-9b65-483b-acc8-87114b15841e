// Google Places API Configuration
// You need to get an API key from Google Cloud Console
// and enable the Places API

export const GOOGLE_PLACES_CONFIG = {
  // Replace with your actual Google Places API key
  API_KEY: 'YOUR_GOOGLE_PLACES_API_KEY',
  
  // Default query parameters
  DEFAULT_QUERY: {
    language: 'en',
    types: '(cities)', // Focus on cities for location search
  },
  
  // Search configuration
  SEARCH_CONFIG: {
    debounce: 300, // Delay before making API calls
    enablePoweredByContainer: false, // Hide "Powered by Google" text
    fetchDetails: true, // Get detailed place information including coordinates
  },
};

// Instructions for getting Google Places API key:
// 1. Go to Google Cloud Console (https://console.cloud.google.com/)
// 2. Create a new project or select existing one
// 3. Enable the Places API
// 4. Create credentials (API Key)
// 5. Restrict the API key to your app (optional but recommended)
// 6. Replace 'YOUR_GOOGLE_PLACES_API_KEY' above with your actual key
