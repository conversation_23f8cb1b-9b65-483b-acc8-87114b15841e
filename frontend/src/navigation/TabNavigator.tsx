import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
} from 'react-native';
import { DashboardScreen } from '../screens/DashboardScreen';
import { SettingsScreen } from '../screens/SettingsScreen';
import { useTheme } from '../context/ThemeContext';
import { RestaurantUser } from '../types';

type Tab = 'Dashboard' | 'Settings';

interface TabNavigatorProps {
  onNavigateToAddUser: () => void;
  onNavigateToEditUser: (user: RestaurantUser) => void;
  onNavigateToLogin: () => void;
  onNavigateToProfile: () => void;
  onNavigateToRestaurantList: () => void;
  onNavigateToRestaurantProfile: () => void;
  onNavigateToSettings: () => void;
}

export function TabNavigator({ onNavigateToAddUser, onNavigateToEditUser, onNavigateToLogin, onNavigateToProfile, onNavigateToRestaurantList, onNavigateToRestaurantProfile, onNavigateToSettings }: TabNavigatorProps) {
  const [activeTab, setActiveTab] = useState<Tab>('Dashboard');
  const { theme } = useTheme();

  const renderContent = () => {
    switch (activeTab) {
      case 'Dashboard':
        return (
          <DashboardScreen
            onNavigateToAddUser={onNavigateToAddUser}
            onNavigateToEditUser={onNavigateToEditUser}
            onNavigateToRestaurantList={onNavigateToRestaurantList}
            onNavigateToSettings={onNavigateToSettings}
          />
        );
      case 'Settings':
        return (
          <SettingsScreen
            onNavigateToLogin={onNavigateToLogin}
            onNavigateToProfile={onNavigateToProfile}
            onNavigateToRestaurantList={onNavigateToRestaurantList}
            onNavigateToHome={onNavigateToRestaurantList}
            onNavigateToAddPerson={onNavigateToAddUser}
            isActive={activeTab === 'Settings'}
          />
        );
      default:
        return (
          <DashboardScreen
            onNavigateToAddUser={onNavigateToAddUser}
          />
        );
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    content: {
      flex: 1,
    },
    tabBarContainer: {
      backgroundColor: theme.colors.surface,
      borderTopWidth: 1,
      borderTopColor: theme.colors.border,
    },
    tabBar: {
      flexDirection: 'row',
      paddingHorizontal: theme.spacing.lg,
      paddingVertical: theme.spacing.sm,
    },
    tabButton: {
      flex: 1,
      paddingVertical: theme.spacing.md,
      paddingHorizontal: theme.spacing.lg,
      borderRadius: theme.borderRadius.md,
      alignItems: 'center',
      marginHorizontal: theme.spacing.xs,
    },
    activeTabButton: {
      backgroundColor: theme.colors.primary,
    },
    tabButtonText: {
      fontSize: theme.typography.fontSize.sm,
      fontWeight: '600',
      color: theme.colors.textSecondary,
    },
    activeTabButtonText: {
      color: theme.colors.white,
    },
  });

  const TabButton = ({ tab, title }: { tab: Tab; title: string }) => (
    <TouchableOpacity
      style={[
        styles.tabButton,
        activeTab === tab && styles.activeTabButton,
      ]}
      onPress={() => setActiveTab(tab)}
    >
      <Text
        style={[
          styles.tabButtonText,
          activeTab === tab && styles.activeTabButtonText,
        ]}
      >
        {title}
      </Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {renderContent()}
    </View>
  );
}
