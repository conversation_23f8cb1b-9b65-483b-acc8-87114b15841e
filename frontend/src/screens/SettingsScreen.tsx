import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  Image,
} from 'react-native';
import { Card } from '../components/Card';
import { Button } from '../components/Button';
import { AppHeader } from '../components/AppHeader';
import { FooterNavigation } from '../components/FooterNavigation';
import { useAuth } from '../context/AuthContext';
import { useTheme } from '../context/ThemeContext';
import { apiService } from '../services/api';

interface SettingsScreenProps {
  onNavigateToLogin: () => void;
  onNavigateToProfile: () => void;
  onNavigateToRestaurantList: () => void;
  onNavigateToHome?: () => void;
  onNavigateToAddPerson?: () => void;
  isActive?: boolean; // Add prop to track when screen is active
}

export function SettingsScreen({ onNavigateToLogin, onNavigateToProfile, onNavigateToRestaurantList, onNavigateToHome, onNavigateToAddPerson, isActive }: SettingsScreenProps) {
  const { user, logout } = useAuth();
  const { theme } = useTheme();
  const [restaurantData, setRestaurantData] = useState<any>(null);

  // Fetch restaurant data to get owner name
  const fetchRestaurantData = useCallback(async () => {
    if (!user) return;

    try {
      const response = await apiService.getMyRestaurant();
      setRestaurantData(response.data);
    } catch (error) {
      console.error('SettingsScreen: Error fetching restaurant data:', error);
    }
  }, [user]);

  // Refresh restaurant data when screen becomes active
  useEffect(() => {
    if (isActive) {
      fetchRestaurantData();
    }
  }, [isActive, fetchRestaurantData]);

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              await logout();
              onNavigateToRestaurantList();
            } catch (error) {
              console.error('Logout error:', error);
              // Navigate anyway since we want to clear local state
              onNavigateToRestaurantList();
            }
          },
        },
      ]
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollView: {
      flex: 1,
    },
    header: {
      paddingHorizontal: theme.spacing.lg,
      paddingVertical: theme.spacing.xl,
    },
    title: {
      fontSize: theme.typography.fontSize.xl,
      fontWeight: 'bold',
      color: theme.colors.text,
    },
    section: {
      marginHorizontal: theme.spacing.lg,
      marginBottom: theme.spacing.lg,
    },
    sectionTitle: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: theme.spacing.md,
    },
    profileContainer: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: theme.spacing.sm,
    },
    profileImageContainer: {
      marginRight: theme.spacing.md,
    },
    profileImage: {
      width: 60,
      height: 60,
      borderRadius: 30,
      borderWidth: 2,
      borderColor: theme.colors.primary,
    },
    profileImagePlaceholder: {
      width: 60,
      height: 60,
      borderRadius: 30,
      backgroundColor: theme.colors.primary + '20',
      borderWidth: 2,
      borderColor: theme.colors.primary,
      justifyContent: 'center',
      alignItems: 'center',
    },
    profileImagePlaceholderText: {
      fontSize: 24,
      fontWeight: 'bold',
      color: theme.colors.primary,
    },
    profileInfo: {
      flex: 1,
      paddingVertical: theme.spacing.sm,
    },
    profileName: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: '600',
      color: theme.colors.text,
      marginBottom: theme.spacing.xs,
    },
    profileEmail: {
      fontSize: theme.typography.fontSize.md,
      color: theme.colors.textSecondary,
    },
    restaurantName: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
      fontStyle: 'italic',
      marginTop: theme.spacing.xs,
    },
    settingItem: {
      paddingVertical: theme.spacing.md,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    settingTitle: {
      fontSize: theme.typography.fontSize.md,
      fontWeight: '500',
      color: theme.colors.text,
      marginBottom: theme.spacing.xs,
    },
    settingSubtitle: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
    },
    logoutButton: {
      marginTop: theme.spacing.md,
    },
    logoutButtonText: {
      color: theme.colors.error,
    },
    footer: {
      paddingHorizontal: theme.spacing.lg,
      paddingVertical: theme.spacing.xl,
      alignItems: 'center',
    },
    footerText: {
      fontSize: theme.typography.fontSize.xs,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      marginBottom: theme.spacing.xs,
    },
    backButton: {
      backgroundColor: theme.colors.primary,
      borderRadius: 20,
      paddingHorizontal: 16,
      paddingVertical: 8,
    },
    backButtonText: {
      color: 'white',
      fontSize: 14,
      fontWeight: '600',
    },
  });

  const SettingItem = ({
    title, 
    subtitle, 
    onPress 
  }: { 
    title: string; 
    subtitle?: string; 
    onPress?: () => void; 
  }) => (
    <TouchableOpacity style={styles.settingItem} onPress={onPress} disabled={!onPress}>
      <View>
        <Text style={styles.settingTitle}>{title}</Text>
        {subtitle && <Text style={styles.settingSubtitle}>{subtitle}</Text>}
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <AppHeader
        title="Settings"
        showLogo={false}
        showBackButton={true}
        onBackPress={onNavigateToRestaurantList}
        rightComponent={
          <TouchableOpacity
            style={styles.logoutButton}
            onPress={handleLogout}
          >
            <Text style={styles.logoutButtonText}>Logout</Text>
          </TouchableOpacity>
        }
      />
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>

        {/* User Profile Section */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Profile</Text>
          <View style={styles.profileContainer}>
            {/* Profile Image */}
            <View style={styles.profileImageContainer}>
              {restaurantData?.profile ? (
                <Image
                  source={{
                    uri: restaurantData.profile.startsWith('http')
                      ? restaurantData.profile
                      : `http://192.168.1.6:8000/storage/${restaurantData.profile}`
                  }}
                  style={styles.profileImage}
                  resizeMode="cover"
                />
              ) : (
                <View style={styles.profileImagePlaceholder}>
                  <Text style={styles.profileImagePlaceholderText}>
                    {restaurantData?.name ? restaurantData.name.charAt(0).toUpperCase() : user?.name?.charAt(0).toUpperCase() || '👤'}
                  </Text>
                </View>
              )}
            </View>

            {/* Profile Info */}
            <View style={styles.profileInfo}>
              <Text style={styles.profileName}>
                {restaurantData?.owner_name || user?.name}
              </Text>
              <Text style={styles.profileEmail}>{user?.email}</Text>
              {restaurantData?.name && (
                <Text style={styles.restaurantName}>{restaurantData.name}</Text>
              )}
            </View>
          </View>
          <SettingItem
            title="Edit Profile"
            subtitle="Update your business details and information"
            onPress={onNavigateToProfile}
          />
        </Card>





        {/* About Section */}
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>About</Text>
          <SettingItem
            title="Version"
            subtitle="1.0.0"
          />
          <SettingItem
            title="Privacy Policy"
            subtitle="View our privacy policy"
          />
          <SettingItem
            title="Terms of Service"
            subtitle="View terms and conditions"
          />
        </Card>

        {/* Logout Section */}
        <Card style={styles.section}>
          <Button
            title="Logout"
            onPress={handleLogout}
            variant="outline"
            style={styles.logoutButton}
            textStyle={styles.logoutButtonText}
          />
        </Card>

        <View style={styles.footer}>
          <Text style={styles.footerText}>
            Restaurant User Management App
          </Text>
          <Text style={styles.footerText}>
            Made with ❤️ for restaurant management
          </Text>
        </View>
      </ScrollView>

      <FooterNavigation
        currentScreen="Settings"
        onNavigateToHome={() => {
          if (onNavigateToHome) {
            onNavigateToHome();
          }
        }}
        onNavigateToWaitingList={() => {
          if (onNavigateToHome) {
            onNavigateToHome();
          }
        }}
        onNavigateToAddPerson={() => {
          if (onNavigateToAddPerson) {
            onNavigateToAddPerson();
          }
        }}
        onNavigateToSettings={() => {
          // Already on settings page
        }}
        onLogout={handleLogout}
      />
    </SafeAreaView>
  );
}
