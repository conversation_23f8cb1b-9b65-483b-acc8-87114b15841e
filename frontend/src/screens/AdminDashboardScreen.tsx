import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import { useTheme } from '../context/ThemeContext';
import { apiService } from '../services/api';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { AppHeader } from '../components/AppHeader';

interface AdminDashboardScreenProps {
  navigation: {
    replace: (screen: string) => void;
  };
  onNavigateToRestaurantList?: () => void;
}

interface DashboardStats {
  total_restaurant_owners: number;
  total_restaurant_users: number;
  total_users: number;
  recent_registrations: number;
}

interface RestaurantOwner {
  id: number;
  name: string;
  email: string;
  restaurant_users_count: number;
  created_at: string;
}

export default function AdminDashboardScreen({ navigation, onNavigateToRestaurantList }: AdminDashboardScreenProps) {
  const { theme } = useTheme();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [owners, setOwners] = useState<RestaurantOwner[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    header: {
      backgroundColor: theme.colors.primary,
      padding: theme.spacing.lg,
      paddingTop: theme.spacing.xl,
    },
    headerTitle: {
      fontSize: theme.typography.fontSize.xl,
      fontWeight: 'bold',
      color: theme.colors.white,
      textAlign: 'center',
    },
    headerSubtitle: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.white,
      textAlign: 'center',
      marginTop: theme.spacing.sm,
      opacity: 0.9,
    },
    content: {
      flex: 1,
      padding: theme.spacing.lg,
    },
    statsContainer: {
      marginBottom: theme.spacing.xl,
    },
    statsGrid: {
      flexDirection: 'row',
      flexWrap: 'wrap',
      justifyContent: 'space-between',
    },
    statCard: {
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      width: '48%',
      marginBottom: theme.spacing.md,
      ...theme.shadows.sm,
    },
    statValue: {
      fontSize: theme.typography.fontSize.xl,
      fontWeight: 'bold',
      color: theme.colors.primary,
      textAlign: 'center',
    },
    statLabel: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      marginTop: theme.spacing.sm,
    },
    sectionTitle: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: theme.spacing.md,
    },
    ownerCard: {
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      marginBottom: theme.spacing.md,
      ...theme.shadows.sm,
    },
    ownerName: {
      fontSize: theme.typography.fontSize.md,
      fontWeight: '600',
      color: theme.colors.text,
    },
    ownerEmail: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
      marginTop: theme.spacing.xs,
    },
    ownerStats: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.primary,
      marginTop: theme.spacing.sm,
    },
    logoutButton: {
      backgroundColor: theme.colors.error,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      alignItems: 'center',
      marginTop: theme.spacing.lg,
    },
    logoutButtonText: {
      color: theme.colors.white,
      fontSize: theme.typography.fontSize.md,
      fontWeight: '600',
    },
    loadingText: {
      textAlign: 'center',
      color: theme.colors.textSecondary,
      fontSize: theme.typography.fontSize.md,
      marginTop: theme.spacing.xl,
    },
    errorText: {
      textAlign: 'center',
      color: theme.colors.error,
      fontSize: theme.typography.fontSize.md,
      marginTop: theme.spacing.xl,
    },
  });

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // Load stats and restaurant owners
      const [statsResponse, ownersResponse] = await Promise.all([
        apiService.getDashboardStats(),
        apiService.getRestaurantOwners({ per_page: 10 }),
      ]);

      if (statsResponse.success) {
        setStats(statsResponse.data);
      }

      if (ownersResponse.success) {
        setOwners(ownersResponse.data);
      }
    } catch (error: any) {
      console.error('Failed to load dashboard data:', error);
      Alert.alert('Error', 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadDashboardData();
    setRefreshing(false);
  };

  const handleLogout = async () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            await AsyncStorage.multiRemove(['admin_token', 'admin_user_data']);
            navigation.replace('Login');
          },
        },
      ]
    );
  };

  if (loading && !refreshing) {
    return (
      <View style={styles.container}>
        <AppHeader
          title="Admin Dashboard"
          showLogo={true}
          onLogoPress={onNavigateToRestaurantList}
        />
        <Text style={styles.loadingText}>Loading dashboard...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <AppHeader
        title="Admin Dashboard"
        showLogo={true}
        onLogoPress={onNavigateToRestaurantList}
      />
      <View style={styles.header}>
        <Text style={styles.headerSubtitle}>Restaurant Management System</Text>
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        {/* Statistics */}
        {stats && (
          <View style={styles.statsContainer}>
            <Text style={styles.sectionTitle}>Overview</Text>
            <View style={styles.statsGrid}>
              <View style={styles.statCard}>
                <Text style={styles.statValue}>{stats.total_restaurant_owners}</Text>
                <Text style={styles.statLabel}>Restaurant Owners</Text>
              </View>
              <View style={styles.statCard}>
                <Text style={styles.statValue}>{stats.total_restaurant_users}</Text>
                <Text style={styles.statLabel}>Restaurant Users</Text>
              </View>
              <View style={styles.statCard}>
                <Text style={styles.statValue}>{stats.total_users}</Text>
                <Text style={styles.statLabel}>Total Users</Text>
              </View>
              <View style={styles.statCard}>
                <Text style={styles.statValue}>{stats.recent_registrations}</Text>
                <Text style={styles.statLabel}>Recent (7 days)</Text>
              </View>
            </View>
          </View>
        )}

        {/* Restaurant Owners */}
        <View>
          <Text style={styles.sectionTitle}>Top Restaurant Owners</Text>
          {owners.map((owner) => (
            <View key={owner.id} style={styles.ownerCard}>
              <Text style={styles.ownerName}>{owner.name}</Text>
              <Text style={styles.ownerEmail}>{owner.email}</Text>
              <Text style={styles.ownerStats}>
                {owner.restaurant_users_count} restaurant users
              </Text>
            </View>
          ))}
        </View>

        <TouchableOpacity style={styles.logoutButton} onPress={handleLogout}>
          <Text style={styles.logoutButtonText}>Logout</Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
}
