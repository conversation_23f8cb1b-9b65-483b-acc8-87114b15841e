import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Alert,
  SafeAreaView,
} from 'react-native';
import { Button } from '../components/Button';
import { Input } from '../components/Input';
import { IconButton } from '../components/IconButton';
import { Table } from '../components/Table';
import { AppHeader } from '../components/AppHeader';
import { FooterNavigation } from '../components/FooterNavigation';
import { RestaurantUser } from '../types';
import { useOfflineData } from '../hooks/useOfflineData';
import { useRestaurantStatus } from '../hooks/useRestaurantStatus';
import { useTheme } from '../context/ThemeContext';
import { useAuth } from '../context/AuthContext';

interface DashboardScreenProps {
  onNavigateToAddUser: () => void;
  onNavigateToEditUser?: (user: RestaurantUser) => void;
  onNavigateToRestaurantList?: () => void;
  onNavigateToSettings?: () => void;
}

export function DashboardScreen({ onNavigateToAddUser, onNavigateToEditUser, onNavigateToRestaurantList, onNavigateToSettings }: DashboardScreenProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [currentDate, setCurrentDate] = useState('');
  const { theme, toggleTheme, isDark } = useTheme();
  const { logout } = useAuth();
  const {
    users,
    refreshing,
    deleteUser,
    markAsDineIn,
    markAsWaiting,
    markAsDineInDelayed,
    searchUsers,
    refresh,
  } = useOfflineData();

  const {
    isOpen: restaurantIsOpen,
    toggleStatus: toggleRestaurantStatus,
    checkAndAutoClose
  } = useRestaurantStatus();

  // Format current date
  const formatCurrentDate = () => {
    const now = new Date();
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    };
    return now.toLocaleDateString('en-US', options);
  };

  // Set up current date
  useEffect(() => {
    setCurrentDate(formatCurrentDate());

    // Update date at midnight
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0);

    const timeUntilMidnight = tomorrow.getTime() - now.getTime();

    const timer = setTimeout(() => {
      setCurrentDate(formatCurrentDate());
    }, timeUntilMidnight);

    return () => clearTimeout(timer);
  }, []);

  // Handle search with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchQuery.trim()) {
        searchUsers(searchQuery);
      } else {
        searchUsers(''); // Show all users
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [searchQuery, searchUsers]);

  // Monitor waiting count and auto-close restaurant when it reaches 0
  useEffect(() => {
    const currentWaitingCount = users.length;
    checkAndAutoClose(currentWaitingCount);
  }, [users.length, checkAndAutoClose]);

  // Real-time updates for waiting list
  useEffect(() => {
    // Refresh waiting list every 5 seconds
    const intervalId = setInterval(() => {
      if (!searchQuery.trim()) {
        refresh(); // Refresh all data
      } else {
        searchUsers(searchQuery); // Refresh search results
      }
    }, 5000);

    return () => clearInterval(intervalId);
  }, [searchQuery, refresh, searchUsers]);

  const handleRefresh = () => {
    refresh();
  };

  const handleDeleteUser = async (userId: number) => {
    Alert.alert(
      'Delete User',
      'Are you sure you want to delete this user?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteUser(userId);
              // Remove success alert - just delete silently
            } catch (error) {
              Alert.alert('Error', 'Failed to delete user');
            }
          },
        },
      ]
    );
  };

  const handleEditUser = (user: RestaurantUser) => {
    if (onNavigateToEditUser) {
      onNavigateToEditUser(user);
    } else {
      // For now, show an alert since edit screen is not implemented yet
      Alert.alert('Edit User', `Edit functionality for ${user.username} will be implemented soon.`);
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },

    header: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingHorizontal: theme.spacing.lg,
      paddingVertical: theme.spacing.md,
    },
    titleContainer: {
      flex: 1,
    },
    title: {
      fontSize: theme.typography.fontSize.xl,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: theme.spacing.xs,
    },
    dateText: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
      fontWeight: '500',
    },
    headerActions: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: theme.spacing.md,
    },
    themeToggle: {
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius.md,
    },
    searchContainer: {
      paddingHorizontal: theme.spacing.lg,
      marginBottom: theme.spacing.sm,
    },
    listContainer: {
      paddingHorizontal: theme.spacing.lg,
      paddingBottom: theme.spacing.xl,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <AppHeader
        title=""
        showLogo={true}
        showOnlineStatus={true}
        isOnline={restaurantIsOpen}
        onLogoPress={() => {
          console.log('🔄 DashboardScreen: Logo clicked, navigating to add people page');
          onNavigateToAddUser();
        }}
        onStatusToggle={async () => {
          try {
            await toggleRestaurantStatus();
          } catch (error) {
            Alert.alert('Error', 'Failed to update restaurant status. Please try again.');
          }
        }}
      />

      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Text style={styles.title}>Restaurant Users</Text>
          <Text style={styles.dateText}>{currentDate}</Text>
        </View>
        <View style={styles.headerActions}>
          <IconButton
            iconName={isDark ? "sunny" : "moon"}
            onPress={toggleTheme}
            size={20}
            color={theme.colors.text}
            style={styles.themeToggle}
          />
          <Button
            title="Add User"
            onPress={onNavigateToAddUser}
            size="small"
          />
        </View>
      </View>

      <Input
        placeholder="Search users..."
        value={searchQuery}
        onChangeText={setSearchQuery}
        containerStyle={styles.searchContainer}
      />

      <ScrollView
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={theme.colors.primary}
          />
        }
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
      >
        <Table
          data={users}
          onEdit={handleEditUser}
          onDelete={handleDeleteUser}
          onMarkAsDineIn={markAsDineIn}
          onMarkAsWaiting={markAsWaiting}
          onMarkAsDineInDelayed={markAsDineInDelayed}
        />
      </ScrollView>

      <FooterNavigation
        currentScreen="WaitingList"
        onNavigateToHome={() => {
          if (onNavigateToRestaurantList) {
            onNavigateToRestaurantList();
          }
        }}
        onNavigateToWaitingList={() => {
          // Already on waiting list page
        }}
        onNavigateToAddPerson={onNavigateToAddUser}
        onNavigateToSettings={() => {
          if (onNavigateToSettings) {
            onNavigateToSettings();
          }
        }}
        onLogout={() => {
          Alert.alert(
            'Logout',
            'Are you sure you want to logout?',
            [
              { text: 'Cancel', style: 'cancel' },
              {
                text: 'Logout',
                style: 'destructive',
                onPress: () => {
                  logout();
                  if (onNavigateToRestaurantList) {
                    onNavigateToRestaurantList();
                  }
                }
              },
            ]
          );
        }}
      />
    </SafeAreaView>
  );
}
