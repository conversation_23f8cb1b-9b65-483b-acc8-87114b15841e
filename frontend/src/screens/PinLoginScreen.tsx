import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useTheme } from '../context/ThemeContext';
import { apiService } from '../services/api';
import { AppHeader } from '../components/AppHeader';

interface PinLoginScreenProps {
  navigation: {
    navigate: (screen: string) => void;
    goBack: () => void;
    replace: (screen: string) => void;
  };
  onNavigateToRestaurantList?: () => void;
}

export default function PinLoginScreen({ navigation, onNavigateToRestaurantList }: PinLoginScreenProps) {
  const { theme } = useTheme();
  const [email, setEmail] = useState('');
  const [pin, setPin] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [pinInputRef, setPinInputRef] = useState<TextInput | null>(null);

  useEffect(() => {
    // Load saved email if available
    loadSavedEmail();
  }, []);

  useEffect(() => {
    // Auto-focus PIN input when email is loaded
    if (email && pinInputRef) {
      setTimeout(() => {
        pinInputRef.focus();
      }, 500);
    }
  }, [email, pinInputRef]);

  const loadSavedEmail = async () => {
    try {
      const savedEmail = await AsyncStorage.getItem('last_login_email');
      if (savedEmail) {
        setEmail(savedEmail);
      }
    } catch (error) {
      console.error('Failed to load saved email:', error);
    }
  };

  const handleUseDifferentAccount = async () => {
    try {
      // Clear the pre-filled email and allow editing
      setEmail('');
      setPin('');
      // Navigate back to main login screen
      navigation.navigate('Login');
    } catch (error) {
      console.error('Error clearing account data:', error);
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    keyboardView: {
      flex: 1,
    },
    scrollView: {
      flexGrow: 1,
      justifyContent: 'center',
      padding: theme.spacing.lg,
    },
    backButtonText: {
      color: theme.colors.primary,
      fontSize: 16,
    },
    title: {
      fontSize: theme.typography.fontSize.xl,
      fontWeight: 'bold',
      color: theme.colors.text,
      textAlign: 'center',
      marginBottom: theme.spacing.md,
    },
    subtitle: {
      fontSize: theme.typography.fontSize.md,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      marginBottom: theme.spacing.xl,
    },
    inputContainer: {
      marginBottom: theme.spacing.lg,
    },
    label: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
      fontWeight: '500',
    },
    input: {
      borderWidth: 1,
      borderColor: theme.colors.border,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      fontSize: theme.typography.fontSize.md,
      color: theme.colors.text,
      backgroundColor: theme.colors.surface,
    },
    pinInput: {
      textAlign: 'center',
      letterSpacing: 8,
      fontSize: theme.typography.fontSize.lg,
    },
    inputReadOnly: {
      backgroundColor: theme.colors.border,
      color: theme.colors.textSecondary,
    },
    inputFocused: {
      borderColor: theme.colors.primary,
    },
    button: {
      backgroundColor: theme.colors.primary,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      alignItems: 'center',
      marginTop: theme.spacing.lg,
    },
    buttonDisabled: {
      backgroundColor: theme.colors.textSecondary,
    },
    buttonText: {
      color: theme.colors.white,
      fontSize: theme.typography.fontSize.md,
      fontWeight: '600',
    },
    alternativeButton: {
      marginTop: theme.spacing.lg,
      alignItems: 'center',
    },
    alternativeButtonText: {
      color: theme.colors.primary,
      fontSize: theme.typography.fontSize.md,
    },
    linkButton: {
      marginTop: theme.spacing.md,
      alignItems: 'center',
    },
    linkText: {
      color: theme.colors.primary,
      fontSize: theme.typography.fontSize.md,
      textDecorationLine: 'underline',
    },
    backButton: {
      marginTop: theme.spacing.md,
      alignItems: 'center',
    },
  });

  const handlePinLogin = async () => {
    if (!email.trim() || !pin.trim()) {
      Alert.alert('Error', 'Please enter both email and PIN');
      return;
    }

    if (pin.length < 4 || pin.length > 6) {
      Alert.alert('Error', 'PIN must be 4-6 digits');
      return;
    }

    setIsLoading(true);
    try {
      const response = await apiService.pinLogin(email.trim(), pin);
      
      if (response.success) {
        // Store auth token and user data
        await AsyncStorage.multiSet([
          ['auth_token', response.data.token],
          ['user_data', JSON.stringify(response.data.user)],
          ['last_login_email', email.trim()],
        ]);
        
        // Navigate to dashboard
        navigation.replace('Main');
      } else {
        Alert.alert('Error', response.message || 'Login failed');
      }
    } catch (error: any) {
      console.error('PIN login error:', error);
      Alert.alert(
        'Error',
        error.response?.data?.message || 'Login failed. Please check your PIN and try again.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <AppHeader
        title="Quick Login"
        showLogo={true}
        onLogoPress={onNavigateToRestaurantList}
        rightComponent={
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Text style={styles.backButtonText}>← Back</Text>
          </TouchableOpacity>
        }
      />
      <KeyboardAvoidingView
        style={styles.keyboardView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView contentContainerStyle={styles.scrollView}>
          <Text style={styles.subtitle}>
            Enter your email and PIN for quick access
          </Text>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>Email</Text>
          <TextInput
            style={[styles.input, email && styles.inputReadOnly]}
            value={email}
            onChangeText={setEmail}
            placeholder="Enter your email"
            placeholderTextColor={theme.colors.textSecondary}
            keyboardType="email-address"
            autoCapitalize="none"
            autoCorrect={false}
            editable={!email} // Make read-only if email is pre-filled
          />
        </View>

        <View style={styles.inputContainer}>
          <Text style={styles.label}>PIN</Text>
          <TextInput
            ref={setPinInputRef}
            style={[styles.input, styles.pinInput]}
            value={pin}
            onChangeText={setPin}
            placeholder="••••"
            placeholderTextColor={theme.colors.textSecondary}
            keyboardType="numeric"
            maxLength={6}
            secureTextEntry
          />
        </View>

        <TouchableOpacity
          style={[styles.button, isLoading && styles.buttonDisabled]}
          onPress={handlePinLogin}
          disabled={isLoading}
        >
          <Text style={styles.buttonText}>
            {isLoading ? 'Logging in...' : 'Login with PIN'}
          </Text>
        </TouchableOpacity>

        {email && (
          <TouchableOpacity
            style={styles.linkButton}
            onPress={handleUseDifferentAccount}
          >
            <Text style={styles.linkText}>Use different account</Text>
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={styles.alternativeButton}
          onPress={() => navigation.navigate('Login')}
        >
          <Text style={styles.alternativeButtonText}>
            Use Google Login Instead
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Text style={styles.backButtonText}>Back</Text>
        </TouchableOpacity>
      </ScrollView>
    </KeyboardAvoidingView>
    </View>
  );
}
