import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  Image,
} from 'react-native';
import { useTheme } from '../context/ThemeContext';
import { useAuth } from '../context/AuthContext';
import { Card } from '../components/Card';
import { Button } from '../components/Button';
import { AppHeader } from '../components/AppHeader';
import { apiService } from '../services/api';
import { locationService } from '../services/locationService';

interface RestaurantProfileScreenProps {
  onNavigateBack: () => void;
  onNavigateToRestaurantList?: () => void;
}

interface RestaurantData {
  id?: number;
  name: string;
  contact_number: string;
  address_line_1: string;
  address_line_2: string;
  city: string;
  state: string;
  country: string;
  postal_code: string;
  description: string;
  profile?: string;
  latitude?: number;
  longitude?: number;
}

export function RestaurantProfileScreen({ onNavigateBack, onNavigateToRestaurantList }: RestaurantProfileScreenProps) {
  const { theme } = useTheme();
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [restaurantData, setRestaurantData] = useState<RestaurantData>({
    name: '',
    contact_number: '',
    address_line_1: '',
    address_line_2: '',
    city: '',
    state: '',
    country: 'India',
    postal_code: '',
    description: '',
  });

  const styles = createStyles(theme);

  useEffect(() => {
    loadRestaurantData();
  }, []);

  const loadRestaurantData = async () => {
    try {
      setLoading(true);
      // Try to load existing restaurant data for this user
      const response = await apiService.get('/restaurants/my-restaurant');
      if (response.success && response.data) {
        setRestaurantData(response.data);
      }
    } catch (error) {
      console.log('No existing restaurant found, creating new profile');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);

      // Validate required fields
      if (!restaurantData.name.trim()) {
        Alert.alert('Error', 'Restaurant name is required');
        return;
      }

      if (!restaurantData.contact_number.trim()) {
        Alert.alert('Error', 'Contact number is required');
        return;
      }

      const dataToSave = {
        ...restaurantData,
        owner_id: user?.id,
        owner_name: user?.name,
      };

      // Use the user-specific endpoint for saving restaurant profile
      const response = await apiService.post('/restaurants/my-restaurant', dataToSave);

      if (response.success) {
        Alert.alert('Success', 'Restaurant profile saved successfully!', [
          { text: 'OK', onPress: onNavigateBack }
        ]);
      } else {
        throw new Error(response.message || 'Failed to save restaurant profile');
      }
    } catch (error) {
      console.error('Error saving restaurant:', error);
      Alert.alert('Error', 'Failed to save restaurant profile. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleAutoFillLocation = async () => {
    try {
      const location = await locationService.getCurrentLocation();
      if (location) {
        const address = await locationService.getAddressFromCoords(location);
        if (address) {
          // Parse address and fill fields (basic implementation)
          const addressParts = address.split(', ');
          setRestaurantData(prev => ({
            ...prev,
            address_line_1: addressParts[0] || '',
            city: addressParts[1] || '',
            state: addressParts[2] || '',
            latitude: location.latitude,
            longitude: location.longitude,
          }));
          Alert.alert('Success', 'Location auto-filled successfully!');
        }
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to get current location. Please enter manually.');
    }
  };

  const updateField = (field: keyof RestaurantData, value: string) => {
    setRestaurantData(prev => ({ ...prev, [field]: value }));
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <AppHeader
          title="Restaurant Profile"
          onLogoPress={onNavigateToRestaurantList}
        />
        <View style={styles.loadingContainer}>
          <Text style={styles.loadingText}>Loading restaurant profile...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <AppHeader
        title="Restaurant Profile"
        onLogoPress={onNavigateToRestaurantList}
        rightComponent={
          <TouchableOpacity onPress={onNavigateBack}>
            <Text style={styles.headerButton}>Done</Text>
          </TouchableOpacity>
        }
      />
      
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        <Card style={styles.section}>
          <Text style={styles.sectionTitle}>Restaurant Profile</Text>

          {/* Profile Image Section */}
          <View style={styles.profileImageSection}>
            <Text style={styles.label}>Restaurant Logo</Text>
            <View style={styles.profileImageContainer}>
              {restaurantData.profile ? (
                <Image
                  source={{
                    uri: restaurantData.profile.startsWith('http')
                      ? restaurantData.profile
                      : `http://192.168.1.38:8000/storage/${restaurantData.profile}`
                  }}
                  style={styles.profileImage}
                  resizeMode="cover"
                />
              ) : (
                <View style={styles.profileImagePlaceholder}>
                  <Text style={styles.profileImagePlaceholderText}>
                    {restaurantData.name ? restaurantData.name.charAt(0).toUpperCase() : '🍽️'}
                  </Text>
                </View>
              )}
            </View>
            <TouchableOpacity style={styles.changeImageButton}>
              <Text style={styles.changeImageButtonText}>Change Logo</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Restaurant Name *</Text>
            <TextInput
              style={styles.input}
              value={restaurantData.name}
              onChangeText={(value) => updateField('name', value)}
              placeholder="Enter restaurant name"
              placeholderTextColor={theme.colors.textSecondary}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Contact Number *</Text>
            <TextInput
              style={styles.input}
              value={restaurantData.contact_number}
              onChangeText={(value) => updateField('contact_number', value)}
              placeholder="Enter contact number"
              placeholderTextColor={theme.colors.textSecondary}
              keyboardType="phone-pad"
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Description</Text>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={restaurantData.description}
              onChangeText={(value) => updateField('description', value)}
              placeholder="Describe your restaurant..."
              placeholderTextColor={theme.colors.textSecondary}
              multiline
              numberOfLines={3}
            />
          </View>
        </Card>

        <Card style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Address</Text>
            <TouchableOpacity 
              style={styles.autoFillButton}
              onPress={handleAutoFillLocation}
            >
              <Text style={styles.autoFillText}>📍 Auto-fill</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Address Line 1</Text>
            <TextInput
              style={styles.input}
              value={restaurantData.address_line_1}
              onChangeText={(value) => updateField('address_line_1', value)}
              placeholder="Street address"
              placeholderTextColor={theme.colors.textSecondary}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={styles.label}>Address Line 2</Text>
            <TextInput
              style={styles.input}
              value={restaurantData.address_line_2}
              onChangeText={(value) => updateField('address_line_2', value)}
              placeholder="Apartment, suite, etc. (optional)"
              placeholderTextColor={theme.colors.textSecondary}
            />
          </View>

          <View style={styles.row}>
            <View style={[styles.inputGroup, styles.flex1]}>
              <Text style={styles.label}>City</Text>
              <TextInput
                style={styles.input}
                value={restaurantData.city}
                onChangeText={(value) => updateField('city', value)}
                placeholder="City"
                placeholderTextColor={theme.colors.textSecondary}
              />
            </View>

            <View style={[styles.inputGroup, styles.flex1, styles.marginLeft]}>
              <Text style={styles.label}>State</Text>
              <TextInput
                style={styles.input}
                value={restaurantData.state}
                onChangeText={(value) => updateField('state', value)}
                placeholder="State"
                placeholderTextColor={theme.colors.textSecondary}
              />
            </View>
          </View>

          <View style={styles.row}>
            <View style={[styles.inputGroup, styles.flex1]}>
              <Text style={styles.label}>Country</Text>
              <TextInput
                style={styles.input}
                value={restaurantData.country}
                onChangeText={(value) => updateField('country', value)}
                placeholder="Country"
                placeholderTextColor={theme.colors.textSecondary}
              />
            </View>

            <View style={[styles.inputGroup, styles.flex1, styles.marginLeft]}>
              <Text style={styles.label}>Postal Code</Text>
              <TextInput
                style={styles.input}
                value={restaurantData.postal_code}
                onChangeText={(value) => updateField('postal_code', value)}
                placeholder="Postal code"
                placeholderTextColor={theme.colors.textSecondary}
                keyboardType="numeric"
              />
            </View>
          </View>
        </Card>

        <View style={styles.buttonContainer}>
          <Button
            title={saving ? "Saving..." : "Save Restaurant Profile"}
            onPress={handleSave}
            disabled={saving}
            variant="primary"
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  headerButton: {
    color: theme.colors.primary,
    fontSize: 16,
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 16,
  },
  inputGroup: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: theme.colors.border,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: theme.colors.text,
    backgroundColor: theme.colors.surface,
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  row: {
    flexDirection: 'row',
  },
  flex1: {
    flex: 1,
  },
  marginLeft: {
    marginLeft: 8,
  },
  autoFillButton: {
    backgroundColor: theme.colors.primary + '20',
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  autoFillText: {
    color: theme.colors.primary,
    fontSize: 12,
    fontWeight: '600',
  },
  buttonContainer: {
    marginTop: 24,
    marginBottom: 32,
  },
  profileImageSection: {
    alignItems: 'center',
    marginBottom: 24,
  },
  profileImageContainer: {
    marginVertical: 12,
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 3,
    borderColor: theme.colors.primary,
  },
  profileImagePlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: theme.colors.primary + '20',
    borderWidth: 2,
    borderColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileImagePlaceholderText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  changeImageButton: {
    backgroundColor: theme.colors.primary + '20',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginTop: 8,
  },
  changeImageButtonText: {
    color: theme.colors.primary,
    fontSize: 14,
    fontWeight: '600',
  },
});
