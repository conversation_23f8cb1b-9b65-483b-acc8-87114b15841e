import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  TextInput,
  Image,
  Alert,
  RefreshControl,
  ActivityIndicator,
  SafeAreaView,
  StatusBar,
  Linking,
} from 'react-native';
import { useTheme } from '../context/ThemeContext';
import { useAuth } from '../context/AuthContext';
import { Card } from '../components/Card';
import { platformConfig } from '../utils/platformConfig';
import { AppHeader } from '../components/AppHeader';
import { FooterNavigation } from '../components/FooterNavigation';
import { LocationSearchInput } from '../components/LocationSearchInput';
import { locationService, LocationCoords } from '../services/locationService';
import { Restaurant } from '../services/restaurantService';
import { apiService } from '../services/api';
import { networkService } from '../services/networkService';
import { syncService } from '../services/sync';

interface RestaurantListScreenProps {
  onNavigateToLogin: () => void;
  onNavigateToAddPerson?: () => void;
  onNavigateToSettings?: () => void;
  onNavigateToHome?: () => void;
}

// Component for handling restaurant images with fallback
const RestaurantImage = ({
  profileUrl,
  restaurantName,
  style
}: {
  profileUrl: string;
  restaurantName: string;
  style: any;
}) => {
  const [imageError, setImageError] = useState(false);
  const [loading, setLoading] = useState(true);

  const getImageUri = (url: string) => {
    if (url.startsWith('http')) {
      return url;
    }

    let finalUrl = '';

    // Get base URL without /api suffix
    const baseUrl = platformConfig.getBaseURL().replace('/api', '');

    // If URL already starts with /storage, just append to base URL
    if (url.startsWith('/storage/')) {
      finalUrl = `${baseUrl}${url}`;
    } else if (url.startsWith('storage/')) {
      // If URL starts with storage/ (without leading slash)
      finalUrl = `${baseUrl}/${url}`;
    } else {
      // Otherwise, assume it's a filename and add full storage path
      finalUrl = `${baseUrl}/storage/${url}`;
    }

    console.log('Image URL constructed:', {
      original: url,
      baseUrl: baseUrl,
      final: finalUrl
    });
    return finalUrl;
  };

  if (imageError) {
    return (
      <View style={[style, { backgroundColor: '#f0f0f0', alignItems: 'center', justifyContent: 'center' }]}>
        <Text style={{ fontSize: 24, color: '#666', fontWeight: 'bold' }}>
          {restaurantName.charAt(0).toUpperCase()}
        </Text>
      </View>
    );
  }

  return (
    <View style={style}>
      <Image
        source={{ uri: getImageUri(profileUrl) }}
        style={style}
        onError={() => {
          setImageError(true);
          setLoading(false);
        }}
        onLoad={() => setLoading(false)}
        onLoadStart={() => setLoading(true)}
      />
      {loading && (
        <View style={[style, { position: 'absolute', alignItems: 'center', justifyContent: 'center', backgroundColor: '#f0f0f0' }]}>
          <ActivityIndicator size="small" color="#666" />
        </View>
      )}
    </View>
  );
};

export function RestaurantListScreen({
  onNavigateToLogin,
  onNavigateToAddPerson,
  onNavigateToSettings,
  onNavigateToHome
}: RestaurantListScreenProps) {
  const { theme } = useTheme();
  const { user } = useAuth();
  const [restaurants, setRestaurants] = useState<Restaurant[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [userLocation, setUserLocation] = useState<LocationCoords | null>(null);
  const [selectedLocation, setSelectedLocation] = useState<string>('');
  const [selectedLocationCoords, setSelectedLocationCoords] = useState<LocationCoords | null>(null);
  const [locationPermission, setLocationPermission] = useState<boolean>(false);
  const [currentLocationName, setCurrentLocationName] = useState<string>('');
  const [isFetching, setIsFetching] = useState(false);

  const styles = createStyles(theme);

  // Filter restaurants based on search query and selected location
  const filteredRestaurants = restaurants.filter(restaurant => {
    // First check location filter
    if (selectedLocation) {
      const locationQuery = selectedLocation.toLowerCase().trim();
      const matchesLocation =
        restaurant.city?.toLowerCase().includes(locationQuery) ||
        restaurant.state?.toLowerCase().includes(locationQuery) ||
        restaurant.location?.toLowerCase().includes(locationQuery) ||
        restaurant.full_address?.toLowerCase().includes(locationQuery);

      if (!matchesLocation) {
        return false;
      }
    }

    // Then check search query filter
    if (!searchQuery) return true;

    const query = searchQuery.toLowerCase().trim();
    const matchesSearch =
      restaurant.name.toLowerCase().includes(query) ||
      restaurant.location?.toLowerCase().includes(query) ||
      restaurant.full_address?.toLowerCase().includes(query) ||
      restaurant.description?.toLowerCase().includes(query) ||
      restaurant.city?.toLowerCase().includes(query) ||
      restaurant.state?.toLowerCase().includes(query) ||
      restaurant.owner_name?.toLowerCase().includes(query);

    return matchesSearch;
  });

  // Popular locations for selection
  const popularLocations = [
    'Mumbai, Maharashtra',
    'Delhi, Delhi',
    'Bangalore, Karnataka',
    'Chennai, Tamil Nadu',
    'Hyderabad, Telangana',
    'Pune, Maharashtra',
    'Kolkata, West Bengal',
    'Ahmedabad, Gujarat',
    'Jaipur, Rajasthan',
    'Lucknow, Uttar Pradesh',
  ];

  useEffect(() => {
    const initializeApp = async () => {
      // Force set the correct backend URL
      platformConfig.forceSetWorkingURL();

      // Test connectivity once on app start
      await apiService.testConnectivity();

      // Request location permission automatically
      requestLocationPermission();
      fetchRestaurants();
    };
    initializeApp();
  }, []);

  useEffect(() => {
    // Refetch restaurants when location changes
    fetchRestaurants();
  }, [selectedLocation, selectedLocationCoords]);

  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      fetchRestaurants();
    }, 500);

    return () => clearTimeout(delayedSearch);
  }, [searchQuery, userLocation]);

  // Real-time updates for waiting counts
  useEffect(() => {
    // Start real-time updates for restaurant list (works for both logged and non-logged users)
    const intervalId = setInterval(() => {
      fetchRestaurants();
    }, 5000); // Refresh every 5 seconds

    // Also listen for sync service updates if user is logged in
    let unsubscribe: (() => void) | null = null;
    if (user) {
      syncService.startRealTimeUpdates();
      unsubscribe = syncService.addRealTimeListener((changedRestaurantIds?: number[]) => {
        if (changedRestaurantIds && changedRestaurantIds.length > 0) {
          console.log(`Updating restaurant list for changed restaurants: ${changedRestaurantIds.join(', ')}`);
          updateSpecificRestaurants(changedRestaurantIds);
        } else {
          // Fallback for general updates
          fetchRestaurants();
        }
      });
    }

    return () => {
      clearInterval(intervalId);
      if (unsubscribe) {
        unsubscribe();
        syncService.stopRealTimeUpdates();
      }
    };
  }, [user]);

  const requestLocationPermission = async () => {
    try {
      const permission = await locationService.requestPermission();
      setLocationPermission(permission.granted);

      if (permission.granted) {
        const location = await locationService.getCurrentLocation();
        if (location) {
          setUserLocation(location);
          // Get location name
          const address = await locationService.getAddressFromCoords(location);
          if (address) {
            const addressParts = address.split(', ');
            const cityName = addressParts.length >= 2 ? addressParts[1] : addressParts[0];
            setCurrentLocationName(cityName || 'Current Location');
          }
        }
      } else {
        Alert.alert(
          'Location Permission',
          'Location permission is needed to show nearby restaurants. You can still browse all restaurants.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error requesting location permission:', error);
      setLocationPermission(false);
    }
  };

  const constructImageUrl = (profileUrl: string): string => {
    if (profileUrl.startsWith('http')) {
      return profileUrl;
    }

    // Get base URL without /api suffix
    const baseUrl = platformConfig.getBaseURL().replace('/api', '');

    // If URL already starts with /storage, just append to base URL
    if (profileUrl.startsWith('/storage/')) {
      return `${baseUrl}${profileUrl}`;
    } else if (profileUrl.startsWith('storage/')) {
      // If URL starts with storage/ (without leading slash)
      return `${baseUrl}/${profileUrl}`;
    } else {
      // Otherwise, assume it's a filename and add full storage path
      return `${baseUrl}/storage/${profileUrl}`;
    }
  };

  const updateSpecificRestaurants = async (changedRestaurantIds: number[]) => {
    try {
      // Fetch fresh restaurant data
      const response = await apiService.get('/restaurants/public');

      if (response.success && response.data?.data) {
        const newRestaurants = response.data.data.map((restaurant: any) => ({
          ...restaurant,
          profile: restaurant.profile ? constructImageUrl(restaurant.profile) : null,
        }));

        // Only update restaurants that actually changed
        setRestaurants(prevRestaurants => {
          return prevRestaurants.map(prevRestaurant => {
            if (changedRestaurantIds.includes(prevRestaurant.id)) {
              const updatedRestaurant = newRestaurants.find(r => r.id === prevRestaurant.id);
              if (updatedRestaurant) {
                console.log(`Updating restaurant ${prevRestaurant.name}: ${prevRestaurant.current_waiting_count} → ${updatedRestaurant.current_waiting_count}`);
                return updatedRestaurant;
              }
            }
            return prevRestaurant; // Keep unchanged restaurants as-is
          });
        });
      }
    } catch (error) {
      console.error('Failed to update specific restaurants:', error);
      // Fallback to full refresh
      fetchRestaurants();
    }
  };

  const fetchRestaurants = async () => {
    // Prevent multiple simultaneous calls
    if (isFetching) return;

    try {
      setIsFetching(true);

      // Build query parameters for location-based search
      let queryParams = '';
      if (selectedLocationCoords) {
        queryParams = `?latitude=${selectedLocationCoords.latitude}&longitude=${selectedLocationCoords.longitude}&radius=50`;
      } else if (searchQuery) {
        queryParams = `?search=${encodeURIComponent(searchQuery)}`;
      }

      // Try restaurants endpoint with retry logic
      const response = await apiService.get(`/restaurants/public${queryParams}`);

      if (response.success && response.data?.data) {
        const processedRestaurants = response.data.data.map((restaurant: any) => ({
          ...restaurant,
          profile: restaurant.profile ? constructImageUrl(restaurant.profile) : null,
        }));
        setRestaurants(processedRestaurants);
      } else {
        throw new Error(response.message || 'Failed to fetch restaurants');
      }
    } catch (error) {
      // Test connectivity to find working URL
      const isConnected = await apiService.testConnectivity();

      if (isConnected) {
        // Retry with the working URL
        try {
          const response = await apiService.get(`/restaurants/public${queryParams}`);
          if (response.success && response.data?.data) {
            const processedRestaurants = response.data.data.map((restaurant: any) => ({
              ...restaurant,
              profile: restaurant.profile ? constructImageUrl(restaurant.profile) : null,
            }));
            setRestaurants(processedRestaurants);
            return; // Success, exit function
          }
        } catch (retryError) {
          // Silent retry failure
        }
      }

      // Fallback to direct fetch
      try {
        const testUrl = `${platformConfig.getBaseURL()}/restaurants/public`.replace('/api/api/', '/api/');

        const response = await fetch(testUrl, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        if (data.success && data.data?.data) {
          setRestaurants(data.data.data.map((restaurant: any) => ({
            ...restaurant,
            profile: restaurant.profile ? constructImageUrl(restaurant.profile) : null,
          })));
        } else {
          throw new Error(data.message || 'Failed to fetch restaurants');
        }
      } catch (fallbackError) {
        // Silent fallback failure

        // Use mock data as final fallback
        const mockRestaurants = [
          {
            id: 1,
            name: 'Pizza Palace',
            location: '123 Main St, New York, NY 10001',
            full_address: '123 Main St, New York, NY 10001',
            address_line_1: '123 Main St',
            address_line_2: null,
            city: 'New York',
            state: 'NY',
            country: 'USA',
            postal_code: '10001',
            contact_number: '+1-555-0101',
            profile: null,
            current_waiting_count: 5,
            distance: null,
            owner_name: 'John Doe',
            description: 'Best pizza in town with authentic Italian flavors',
            is_active: true,
          },
          {
            id: 2,
            name: 'Burger Barn',
            location: '456 Oak Ave, Los Angeles, CA 90210',
            full_address: '456 Oak Ave, Los Angeles, CA 90210',
            address_line_1: '456 Oak Ave',
            address_line_2: null,
            city: 'Los Angeles',
            state: 'CA',
            country: 'USA',
            postal_code: '90210',
            contact_number: '+1-555-0102',
            profile: null,
            current_waiting_count: 3,
            distance: null,
            owner_name: 'Jane Smith',
            description: 'Gourmet burgers made with fresh, local ingredients',
            is_active: false,
          },
          {
            id: 3,
            name: 'Mountain View Cafe',
            location: 'Mall Road, Manali, Himachal Pradesh 175131',
            full_address: 'Mall Road, Manali, Himachal Pradesh 175131',
            address_line_1: 'Mall Road',
            address_line_2: null,
            city: 'Manali',
            state: 'Himachal Pradesh',
            country: 'India',
            postal_code: '175131',
            contact_number: '+91-1902-252001',
            profile: null,
            current_waiting_count: 8,
            distance: null,
            owner_name: 'Rajesh Kumar',
            description: 'Cozy cafe with stunning mountain views and local cuisine',
            is_active: true,
          },
          {
            id: 4,
            name: 'Himalayan Delights',
            location: 'Old Manali Road, Manali, Himachal Pradesh 175131',
            full_address: 'Old Manali Road, Manali, Himachal Pradesh 175131',
            address_line_1: 'Old Manali Road',
            address_line_2: null,
            city: 'Manali',
            state: 'Himachal Pradesh',
            country: 'India',
            postal_code: '175131',
            contact_number: '+91-1902-252002',
            profile: null,
            current_waiting_count: 12,
            distance: null,
            owner_name: 'Priya Sharma',
            description: 'Traditional Himachali food with modern twist',
            is_active: false,
          },
          {
            id: 5,
            name: 'Sushi Spot',
            location: '789 Pine Rd, Chicago, IL 60601',
            full_address: '789 Pine Rd, Chicago, IL 60601',
            address_line_1: '789 Pine Rd',
            address_line_2: null,
            city: 'Chicago',
            state: 'IL',
            country: 'USA',
            postal_code: '60601',
            contact_number: '+1-555-0103',
            profile: null,
            current_waiting_count: 8,
            distance: null,
            owner_name: 'Mike Johnson',
            description: 'Fresh sushi and Japanese cuisine in a modern setting',
            is_active: true,
          },
          {
            id: 6,
            name: 'Rajkot Royal Restaurant',
            location: 'Jawahar Road, Rajkot, Gujarat 360001',
            full_address: 'Jawahar Road, Rajkot, Gujarat 360001',
            address_line_1: 'Jawahar Road',
            address_line_2: null,
            city: 'Rajkot',
            state: 'Gujarat',
            country: 'India',
            postal_code: '360001',
            contact_number: '+91-281-2345678',
            profile: null,
            current_waiting_count: 6,
            distance: null,
            owner_name: 'Kiran Patel',
            description: 'Authentic Gujarati thali and traditional cuisine',
            is_active: true,
          },
          {
            id: 7,
            name: 'Kathiyawadi Kitchen',
            location: 'Race Course Road, Rajkot, Gujarat 360001',
            full_address: 'Race Course Road, Rajkot, Gujarat 360001',
            address_line_1: 'Race Course Road',
            address_line_2: null,
            city: 'Rajkot',
            state: 'Gujarat',
            country: 'India',
            postal_code: '360001',
            contact_number: '+91-281-2345679',
            profile: null,
            current_waiting_count: 4,
            distance: null,
            owner_name: 'Meera Shah',
            description: 'Delicious Kathiyawadi food with authentic flavors',
            is_active: false,
          },
        ];

        setRestaurants(mockRestaurants);
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
      setIsFetching(false);
    }
  };

  const onRefresh = () => {
    setRefreshing(true);
    fetchRestaurants();
  };

  const handleCallRestaurant = (phoneNumber: string) => {
    const url = `tel:${phoneNumber}`;
    Linking.canOpenURL(url)
      .then((supported) => {
        if (supported) {
          Linking.openURL(url);
        } else {
          Alert.alert('Error', 'Phone calls are not supported on this device');
        }
      })
      .catch((error) => {
        console.error('Error opening phone app:', error);
        Alert.alert('Error', 'Failed to open phone app');
      });
  };

  const renderRestaurant = ({ item }: { item: Restaurant }) => (
    <Card style={styles.restaurantCard}>
      <View style={styles.restaurantHeader}>
        <View style={styles.restaurantInfo}>
          <View style={styles.restaurantImageContainer}>
            {item.profile ? (
              <RestaurantImage
                profileUrl={item.profile}
                restaurantName={item.name}
                style={styles.restaurantImage}
              />
            ) : (
              <View style={[styles.restaurantImage, styles.placeholderImage]}>
                <Text style={styles.placeholderText}>{item.name.charAt(0)}</Text>
              </View>
            )}
            <View style={[styles.statusIndicator, item.is_active ? styles.statusOnline : styles.statusOffline]}>
              <Text style={[styles.statusText, item.is_active ? styles.statusTextOnline : styles.statusTextOffline]}>
                {item.is_active ? 'Open' : 'Close'}
              </Text>
            </View>
          </View>
          <View style={styles.restaurantDetails}>
            <Text style={styles.restaurantName}>{item.name}</Text>
            <Text style={styles.restaurantAddress} numberOfLines={2}>
              📍 {item.full_address || item.location}
            </Text>
            {item.description && (
              <Text style={styles.restaurantDescription} numberOfLines={1}>
                {item.description}
              </Text>
            )}
            {item.distance && (
              <Text style={styles.distance}>{item.distance} km away</Text>
            )}
          </View>
        </View>
        <View style={styles.waitingCount}>
          <Text style={styles.waitingCountNumber}>{item.current_waiting_count}</Text>
          <Text style={styles.waitingCountLabel}>Waiting</Text>
        </View>
      </View>

      <View style={styles.restaurantActions}>
        <TouchableOpacity
          style={styles.callButton}
          onPress={() => handleCallRestaurant(item.contact_number)}
        >
          <Text style={styles.callButtonText}>📞 Call</Text>
        </TouchableOpacity>
      </View>
    </Card>
  );

  const handleLocationSelect = (location: string, coords?: LocationCoords) => {
    setSelectedLocation(location);
    setSelectedLocationCoords(coords || null);
  };

  const renderSearchAndLocationBar = () => (
    <View style={styles.searchLocationContainer}>
      {/* Search Input - 70% */}
      <TextInput
        style={styles.searchInput}
        placeholder="Search restaurants..."
        placeholderTextColor={theme.colors.textSecondary}
        value={searchQuery}
        onChangeText={setSearchQuery}
      />

      {/* Location Search - 30% */}
      <LocationSearchInput
        onLocationSelect={handleLocationSelect}
        currentLocation={selectedLocation || currentLocationName}
        placeholder="Select location"
      />
    </View>
  );

  const renderFooter = () => (
    <View style={styles.footer}>
      <Text style={styles.footerText}>
        {restaurants.length > 0
          ? `Found ${restaurants.length} restaurant${restaurants.length !== 1 ? 's' : ''}`
          : 'No restaurants available'
        }
      </Text>
    </View>
  );



  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={theme.colors.primary} />
          <Text style={styles.loadingText}>Loading restaurants...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor={theme.colors.surface}
        translucent={true}
      />
      <AppHeader
        showLogo={true}
        onLogoPress={() => {
          // Already on restaurant list page, just refresh
          fetchRestaurants();
        }}
        rightComponent={
          user ? (
            <TouchableOpacity
              style={styles.addPersonButton}
              onPress={onNavigateToAddPerson}
            >
              <Text style={styles.addPersonButtonText}>➕ Add Person</Text>
            </TouchableOpacity>
          ) : (
            <TouchableOpacity
              style={styles.loginButton}
              onPress={onNavigateToLogin}
            >
              <Text style={styles.loginButtonText}>👤 Login</Text>
            </TouchableOpacity>
          )
        }
      />
      {renderSearchAndLocationBar()}
      <FlatList
        data={filteredRestaurants}
        renderItem={renderRestaurant}
        keyExtractor={(item) => item.id.toString()}
        ListFooterComponent={renderFooter}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={styles.emptyText}>No restaurants found</Text>
            <Text style={styles.emptySubtext}>
              {searchQuery ? 'Try adjusting your search' : 'Check back later for new restaurants'}
            </Text>
          </View>
        }
      />

      {/* Show footer navigation only for authenticated users */}
      {user && (
        <FooterNavigation
          currentScreen="Home"
          onNavigateToHome={() => {
            // Already on restaurant list page
          }}
          onNavigateToWaitingList={() => {
            if (onNavigateToHome) {
              onNavigateToHome();
            }
          }}
          onNavigateToAddPerson={() => {
            if (onNavigateToAddPerson) {
              onNavigateToAddPerson();
            }
          }}
          onNavigateToSettings={() => {
            if (onNavigateToSettings) {
              onNavigateToSettings();
            }
          }}
          onLogout={() => {
            Alert.alert(
              'Logout',
              'Are you sure you want to logout?',
              [
                { text: 'Cancel', style: 'cancel' },
                {
                  text: 'Logout',
                  style: 'destructive',
                  onPress: () => {
                    // Handle logout - this should be passed from parent
                  }
                },
              ]
            );
          }}
        />
      )}
    </View>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },

  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  listContainer: {
    padding: 16,
  },
  searchLocationContainer: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: 8,
    gap: 12,
    backgroundColor: theme.colors.background,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    alignItems: 'center',
  },
  loginButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  loginButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  addPersonButton: {
    backgroundColor: theme.colors.success || theme.colors.primary,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  addPersonButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  searchInput: {
    flex: 0.7,
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    color: theme.colors.text,
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  locationButton: {
    flex: 0.3,
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: theme.colors.border,
    justifyContent: 'center',
    alignItems: 'center',
  },
  locationButtonText: {
    fontSize: 12,
    color: theme.colors.text,
    fontWeight: '500',
    textAlign: 'center',
  },

  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginBottom: 16,
  },
  restaurantCard: {
    marginBottom: 16,
  },
  restaurantHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  restaurantInfo: {
    flexDirection: 'row',
    flex: 1,
  },
  restaurantImageContainer: {
    alignItems: 'center',
    marginRight: 12,
  },
  restaurantImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginBottom: 4,
  },
  placeholderImage: {
    backgroundColor: theme.colors.border,
    justifyContent: 'center',
    alignItems: 'center',
  },
  placeholderText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.textSecondary,
  },
  restaurantDetails: {
    flex: 1,
  },
  restaurantNameRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  restaurantName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    flex: 1,
  },
  statusIndicator: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    alignSelf: 'center',
    minWidth: 50,
    alignItems: 'center',
  },
  statusOnline: {
    backgroundColor: '#10B981',
  },
  statusOffline: {
    backgroundColor: '#EF4444',
  },
  statusText: {
    fontSize: 10,
    fontWeight: '600',
  },
  statusTextOnline: {
    color: 'white',
  },
  statusTextOffline: {
    color: 'white',
  },
  restaurantAddress: {
    fontSize: 13,
    color: theme.colors.textSecondary,
    marginTop: 4,
    marginBottom: 4,
    lineHeight: 18,
  },
  restaurantDescription: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginBottom: 4,
    fontStyle: 'italic',
  },

  distance: {
    fontSize: 12,
    color: theme.colors.primary,
    fontWeight: '500',
  },
  waitingCount: {
    alignItems: 'center',
    backgroundColor: theme.colors.primary + '20',
    borderRadius: 8,
    padding: 8,
    minWidth: 60,
  },
  waitingCountNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  waitingCountLabel: {
    fontSize: 12,
    color: theme.colors.primary,
  },
  restaurantActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  callButton: {
    backgroundColor: theme.colors.success,
    borderRadius: 6,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  callButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  footer: {
    marginTop: 32,
    padding: 20,
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
  },
  footerText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '500',
    color: theme.colors.textSecondary,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: theme.colors.background,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '70%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
  },
  modalCloseButton: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: theme.colors.border,
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalCloseText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
  },
  locationList: {
    maxHeight: 400,
  },
  locationItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  locationItemSpecial: {
    backgroundColor: theme.colors.primary + '10',
  },
  locationItemText: {
    fontSize: 16,
    color: theme.colors.text,
  },
  locationItemTextSpecial: {
    color: theme.colors.primary,
    fontWeight: '600',
  },
  locationItemSelected: {
    fontSize: 16,
    color: theme.colors.primary,
    fontWeight: 'bold',
  },
});
