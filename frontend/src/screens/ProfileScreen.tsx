import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
  Image,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import { useTheme } from '../context/ThemeContext';
import { useAuth } from '../context/AuthContext';
import { apiService } from '../services/api';
import { Card } from '../components/Card';
import { Button } from '../components/Button';
import { Input } from '../components/Input';
import { AppHeader } from '../components/AppHeader';
import { FooterNavigation } from '../components/FooterNavigation';
import { locationService } from '../services/locationService';

interface ProfileScreenProps {
  onNavigateBack: () => void;
  onNavigateToLogin?: () => void;
  onNavigateToRestaurantList?: () => void;
  onNavigateToHome?: () => void;
  onNavigateToAddPerson?: () => void;
  onNavigateToSettings?: () => void;
}

interface RestaurantData {
  id?: number;
  name: string;
  contact_number: string;
  address_line_1: string;
  address_line_2: string;
  city: string;
  state: string;
  country: string;
  postal_code: string;
  description: string;
  profile?: string;
  latitude?: number;
  longitude?: number;
  operating_hours?: string;
  cuisine_type?: string;
  website?: string;
  owner_name?: string;
}

export default function ProfileScreen({ onNavigateBack, onNavigateToLogin, onNavigateToRestaurantList, onNavigateToHome, onNavigateToAddPerson, onNavigateToSettings }: ProfileScreenProps) {
  const { theme } = useTheme();
  const { user, logout } = useAuth();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [showPinChange, setShowPinChange] = useState(false);
  const [newPin, setNewPin] = useState('');
  const [confirmPin, setConfirmPin] = useState('');
  const [restaurantData, setRestaurantData] = useState<RestaurantData>({
    name: '',
    contact_number: '',
    address_line_1: '',
    address_line_2: '',
    city: '',
    state: '',
    country: '',
    postal_code: '',
    description: '',
    operating_hours: '',
    cuisine_type: '',
    website: '',
    owner_name: '',
  });
  const [profileImage, setProfileImage] = useState<string | null>(null);

  // Load restaurant data on component mount
  useEffect(() => {
    loadRestaurantData();
  }, []);

  const loadRestaurantData = async () => {
    try {
      setLoading(true);
      const response = await apiService.getRestaurantProfile();
      if (response.data) {
        setRestaurantData(response.data);
        // Set profile image with full URL if it exists
        if (response.data.profile) {
          const imageUrl = response.data.profile.startsWith('http')
            ? response.data.profile
            : `http://***********:8000/storage/${response.data.profile}`;
          setProfileImage(imageUrl);
        }
      }
    } catch (error) {
      console.error('Error loading restaurant data:', error);
      Alert.alert('Error', 'Failed to load restaurant data');
    } finally {
      setLoading(false);
    }
  };

  const handleImagePicker = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Please grant camera roll permissions to upload images.');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        setProfileImage(result.assets[0].uri);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Error', 'Failed to pick image');
    }
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      console.log('💾 Starting comprehensive save process...');

      // Validate required fields
      if (!restaurantData.name.trim()) {
        Alert.alert('Validation Error', 'Restaurant name is required');
        return;
      }

      if (!restaurantData.contact_number.trim()) {
        Alert.alert('Validation Error', 'Contact number is required');
        return;
      }

      // Validate contact number (10 digits)
      const cleanedPhone = restaurantData.contact_number.replace(/\D/g, '');
      if (cleanedPhone.length !== 10) {
        Alert.alert('Validation Error', 'Contact number must be exactly 10 digits');
        return;
      }

      // Step 1: Save PIN if changed
      if (showPinChange && newPin && confirmPin) {
        if (newPin !== confirmPin) {
          Alert.alert('Error', 'PINs do not match');
          return;
        }
        if (newPin.length < 4 || newPin.length > 6) {
          Alert.alert('Error', 'PIN must be 4-6 digits');
          return;
        }
        if (!/^\d+$/.test(newPin)) {
          Alert.alert('Error', 'PIN must contain only numbers');
          return;
        }

        console.log('🔐 Saving PIN...');
        try {
          const pinResponse = await apiService.setPin(newPin);
          if (!pinResponse.success) {
            Alert.alert('Error', pinResponse.message || 'Failed to update PIN');
            return;
          }
          console.log('✅ PIN saved successfully');
        } catch (pinError) {
          console.error('❌ PIN save error:', pinError);
          Alert.alert('Error', 'Failed to update PIN');
          return;
        }
      }

      // Step 2: Prepare restaurant profile data
      console.log('📋 Preparing restaurant profile data...');

      // Check if we have a new profile image to upload
      const hasNewImage = profileImage && profileImage.startsWith('file://');

      if (hasNewImage) {
        // Use FormData for image upload
        const formData = new FormData();

        // Add all restaurant data fields (skip website temporarily)
        Object.keys(restaurantData).forEach(key => {
          let value = restaurantData[key as keyof RestaurantData];
          if (value !== undefined && value !== null && value !== '') {
            // Skip website field temporarily for testing
            if (key === 'website') {
              console.log('🌐 FormData - Skipping website field for testing:', value);
              return;
            }

            formData.append(key, value.toString());
          }
        });

        // Add profile image
        const imageUri = profileImage;
        const filename = imageUri.split('/').pop() || 'profile.jpg';
        const match = /\.(\w+)$/.exec(filename);
        const type = match ? `image/${match[1]}` : 'image/jpeg';

        formData.append('profile', {
          uri: imageUri,
          type: type,
          name: filename,
        } as any);

        console.log('📷 Uploading profile with image...');
        const response = await apiService.updateRestaurantProfile(formData);

        if (response.success) {
          console.log('✅ Profile with image saved successfully');
        } else {
          console.error('❌ Profile save error:', response);
          Alert.alert('Error', response.message || 'Failed to update profile');
          return;
        }
      } else {
        // Use JSON for data-only update
        const dataToSend = {
          name: restaurantData.name,
          contact_number: restaurantData.contact_number,
          address_line_1: restaurantData.address_line_1,
          address_line_2: restaurantData.address_line_2,
          city: restaurantData.city,
          state: restaurantData.state,
          country: restaurantData.country,
          postal_code: restaurantData.postal_code,
          description: restaurantData.description,
          // website: restaurantData.website, // Temporarily disabled for testing
          owner_name: restaurantData.owner_name,
          latitude: restaurantData.latitude,
          longitude: restaurantData.longitude,
        };

        // Remove empty fields and handle special cases
        Object.keys(dataToSend).forEach(key => {
          const value = dataToSend[key as keyof typeof dataToSend];
          if (value === undefined || value === null || value === '') {
            delete dataToSend[key as keyof typeof dataToSend];
          }
        });

        // Handle website field - convert to lowercase and add protocol if needed
        if (dataToSend.website && dataToSend.website.trim()) {
          let website = dataToSend.website.trim().toLowerCase();

          console.log('🌐 Original website:', dataToSend.website);
          console.log('🌐 Trimmed lowercase website:', website);

          // Add https:// if no protocol is specified
          if (!website.startsWith('http://') && !website.startsWith('https://')) {
            website = `https://${website}`;
            console.log('🌐 Added protocol, final website:', website);
          }

          dataToSend.website = website;
          console.log('🌐 Website in dataToSend:', dataToSend.website);
        } else {
          console.log('🌐 No website provided or empty');
        }

        console.log('📋 Saving profile data:', dataToSend);
        console.log('🔍 Owner name in data:', dataToSend.owner_name);
        console.log('🌐 Website in data:', dataToSend.website);
        console.log('📊 All fields being sent:', Object.keys(dataToSend));

        const response = await apiService.updateRestaurantProfileJson(dataToSend);
        console.log('📤 API Response:', response);

        if (response.success) {
          console.log('✅ Profile data saved successfully');
        } else {
          console.error('❌ Profile save error:', response);
          Alert.alert('Error', response.message || 'Failed to update profile');
          return;
        }
      }

      // Step 3: Reset PIN change form if it was used
      if (showPinChange) {
        setShowPinChange(false);
        setNewPin('');
        setConfirmPin('');
      }

      // Step 4: Auto-navigate to Settings after successful save
      onNavigateBack();

    } catch (error) {
      console.error('❌ Comprehensive save error:', error);
      Alert.alert('Error', 'Failed to save business profile. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleLocationFill = async () => {
    try {
      console.log('🔍 Getting current location...');
      const location = await locationService.getCurrentLocation();
      console.log('📍 Location received:', location);

      if (location) {
        console.log('🔍 Getting detailed address from coordinates...');
        const addressDetails = await locationService.getDetailedAddressFromCoords(location);
        console.log('🏠 Address details:', addressDetails);

        if (addressDetails) {
          setRestaurantData(prev => ({
            ...prev,
            address_line_1: addressDetails.street || '',
            city: addressDetails.city || '',
            state: addressDetails.state || '',
            country: addressDetails.country || 'India',
            postal_code: addressDetails.postalCode || '',
            latitude: location.latitude,
            longitude: location.longitude,
          }));
          Alert.alert('Success', 'Address filled from current location');
        } else {
          Alert.alert('Info', 'Could not get address details, but location coordinates saved');
          setRestaurantData(prev => ({
            ...prev,
            latitude: location.latitude,
            longitude: location.longitude,
          }));
        }
      } else {
        Alert.alert('Error', 'Could not get current location. Please check location permissions.');
      }
    } catch (error) {
      console.error('Error getting location:', error);
      Alert.alert('Error', 'Failed to get current location. Please check location permissions.');
    }
  };



  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              await logout();
              if (onNavigateToRestaurantList) {
                onNavigateToRestaurantList();
              }
            } catch (error) {
              console.error('Logout error:', error);
              if (onNavigateToRestaurantList) {
                onNavigateToRestaurantList();
              }
            }
          },
        },
      ]
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    scrollView: {
      flex: 1,
    },
    content: {
      padding: theme.spacing.lg,
    },
    section: {
      marginBottom: theme.spacing.lg,
    },
    sectionTitle: {
      fontSize: theme.typography.fontSize.lg,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: theme.spacing.md,
    },
    profileImageContainer: {
      alignItems: 'center',
      marginBottom: theme.spacing.lg,
    },
    profileImage: {
      width: 120,
      height: 120,
      borderRadius: 60,
      backgroundColor: theme.colors.surface,
    },
    profileImagePlaceholder: {
      width: 120,
      height: 120,
      borderRadius: 60,
      backgroundColor: theme.colors.surface,
      alignItems: 'center',
      justifyContent: 'center',
      borderWidth: 2,
      borderColor: theme.colors.border,
      borderStyle: 'dashed',
    },
    profileImageText: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
      textAlign: 'center',
      marginTop: theme.spacing.xs,
    },
    inputRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: theme.spacing.md,
    },
    inputHalf: {
      flex: 0.48,
    },
    textArea: {
      height: 100,
      textAlignVertical: 'top',
    },
    locationButton: {
      backgroundColor: theme.colors.primary,
      padding: theme.spacing.sm,
      borderRadius: theme.borderRadius.md,
      alignItems: 'center',
      marginBottom: theme.spacing.md,
    },
    locationButtonText: {
      color: 'white',
      fontSize: theme.typography.fontSize.sm,
      fontWeight: '600',
    },
    saveButton: {
      backgroundColor: theme.colors.primary,
      padding: theme.spacing.md,
      borderRadius: theme.borderRadius.md,
      alignItems: 'center',
      marginTop: theme.spacing.lg,
    },
    saveButtonText: {
      color: 'white',
      fontSize: theme.typography.fontSize.md,
      fontWeight: '600',
    },
    saveButtonDisabled: {
      backgroundColor: theme.colors.textSecondary,
    },
    backButton: {
      backgroundColor: theme.colors.primary,
      borderRadius: 20,
      paddingHorizontal: 16,
      paddingVertical: 8,
    },
    backButtonText: {
      color: 'white',
      fontSize: 14,
      fontWeight: '600',
    },
    saveHeaderButton: {
      backgroundColor: theme.colors.primary,
      borderRadius: 20,
      paddingHorizontal: 16,
      paddingVertical: 8,
    },
    saveHeaderButtonText: {
      color: 'white',
      fontSize: 14,
      fontWeight: '600',
    },
    disabledInput: {
      backgroundColor: theme.colors.background,
      color: theme.colors.textSecondary,
    },
    pinChangeButton: {
      backgroundColor: theme.colors.primary,
      padding: theme.spacing.md,
      borderRadius: theme.borderRadius.md,
      alignItems: 'center',
    },
    pinChangeButtonText: {
      color: 'white',
      fontSize: theme.typography.fontSize.md,
      fontWeight: '600',
    },
    pinButtonRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginTop: theme.spacing.md,
      gap: theme.spacing.md,
    },
    pinButton: {
      flex: 1,
      padding: theme.spacing.md,
      borderRadius: theme.borderRadius.md,
      alignItems: 'center',
    },
    pinCancelButton: {
      backgroundColor: theme.colors.textSecondary,
    },
    pinSaveButton: {
      backgroundColor: theme.colors.primary,
    },
    pinCancelButtonText: {
      color: 'white',
      fontSize: theme.typography.fontSize.md,
      fontWeight: '600',
    },
    pinSaveButtonText: {
      color: 'white',
      fontSize: theme.typography.fontSize.md,
      fontWeight: '600',
    },
    pinInfoContainer: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
      paddingHorizontal: theme.spacing.sm,
    },
    pinInfoText: {
      color: theme.colors.textSecondary,
      fontSize: theme.typography.fontSize.sm,
      textAlign: 'center',
      fontStyle: 'italic',
    },
    button: {
      backgroundColor: theme.colors.primary,
      borderRadius: theme.borderRadius.md,
      padding: theme.spacing.md,
      alignItems: 'center',
      marginTop: theme.spacing.md,
    },
    buttonSecondary: {
      backgroundColor: theme.colors.textSecondary,
    },
    buttonDanger: {
      backgroundColor: theme.colors.error,
    },
    buttonText: {
      color: theme.colors.white,
      fontSize: theme.typography.fontSize.md,
      fontWeight: '600',
    },
    buttonRow: {
      flexDirection: 'row',
      gap: theme.spacing.md,
    },
    buttonFlex: {
      flex: 1,
    },
    errorText: {
      color: theme.colors.error,
      fontSize: theme.typography.fontSize.sm,
      marginTop: theme.spacing.sm,
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <AppHeader
        title="Business Profile"
        showLogo={false}
        showBackButton={true}
        onBackPress={onNavigateBack}
        rightComponent={
          <TouchableOpacity
            style={[styles.saveHeaderButton, (saving || loading) && styles.saveButtonDisabled]}
            onPress={handleSave}
            disabled={saving || loading}
          >
            <Text style={styles.saveHeaderButtonText}>
              {saving ? "Saving..." : "💾 Save All"}
            </Text>
          </TouchableOpacity>
        }
      />

      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          <View style={styles.content}>

            {/* Profile Image Section */}
            <Card style={styles.section}>
              <Text style={styles.sectionTitle}>Restaurant Logo</Text>
              <View style={styles.profileImageContainer}>
                <TouchableOpacity onPress={handleImagePicker}>
                  {profileImage ? (
                    <Image source={{ uri: profileImage }} style={styles.profileImage} />
                  ) : (
                    <View style={styles.profileImagePlaceholder}>
                      <Text style={styles.profileImageText}>📷{'\n'}Tap to add logo</Text>
                    </View>
                  )}
                </TouchableOpacity>
              </View>
            </Card>

            {/* Basic Information */}
            <Card style={styles.section}>
              <Text style={styles.sectionTitle}>Basic Information</Text>

              <Input
                label="Owner Name"
                value={restaurantData.owner_name || user?.name || ''}
                onChangeText={(text) => setRestaurantData(prev => ({ ...prev, owner_name: text }))}
                placeholder="Enter owner name"
              />

              <Input
                label="Email"
                value={user?.email || ''}
                editable={false}
                style={styles.disabledInput}
              />

              <Input
                label="Restaurant Name *"
                value={restaurantData.name}
                onChangeText={(text) => setRestaurantData(prev => ({ ...prev, name: text }))}
                placeholder="Enter restaurant name"
              />

              <Input
                label="Contact Number *"
                value={restaurantData.contact_number}
                onChangeText={(text) => {
                  // Only allow numbers and limit to 10 digits
                  const numbersOnly = text.replace(/\D/g, '').slice(0, 10);
                  setRestaurantData(prev => ({ ...prev, contact_number: numbersOnly }));
                }}
                placeholder="Enter 10-digit contact number"
                keyboardType="phone-pad"
                maxLength={10}
              />

              <Input
                label="Website"
                value={restaurantData.website || ''}
                onChangeText={(text) => {
                  // Convert to lowercase automatically
                  const lowercaseText = text.toLowerCase();
                  setRestaurantData(prev => ({ ...prev, website: lowercaseText }));
                }}
                placeholder="https://www.yourrestaurant.com"
                keyboardType="url"
              />
            </Card>

            {/* Address Information */}
            <Card style={styles.section}>
              <Text style={styles.sectionTitle}>Address Information</Text>

              <TouchableOpacity style={styles.locationButton} onPress={handleLocationFill}>
                <Text style={styles.locationButtonText}>📍 Use Current Location</Text>
              </TouchableOpacity>

              <Input
                label="Address Line 1"
                value={restaurantData.address_line_1}
                onChangeText={(text) => setRestaurantData(prev => ({ ...prev, address_line_1: text }))}
                placeholder="Street address"
              />

              <Input
                label="Address Line 2"
                value={restaurantData.address_line_2}
                onChangeText={(text) => setRestaurantData(prev => ({ ...prev, address_line_2: text }))}
                placeholder="Apartment, suite, etc. (optional)"
              />

              <View style={styles.inputRow}>
                <View style={styles.inputHalf}>
                  <Input
                    label="City"
                    value={restaurantData.city}
                    onChangeText={(text) => setRestaurantData(prev => ({ ...prev, city: text }))}
                    placeholder="City"
                  />
                </View>
                <View style={styles.inputHalf}>
                  <Input
                    label="State"
                    value={restaurantData.state}
                    onChangeText={(text) => setRestaurantData(prev => ({ ...prev, state: text }))}
                    placeholder="State"
                  />
                </View>
              </View>

              <View style={styles.inputRow}>
                <View style={styles.inputHalf}>
                  <Input
                    label="Country"
                    value={restaurantData.country}
                    onChangeText={(text) => setRestaurantData(prev => ({ ...prev, country: text }))}
                    placeholder="Country"
                  />
                </View>
                <View style={styles.inputHalf}>
                  <Input
                    label="Postal Code"
                    value={restaurantData.postal_code}
                    onChangeText={(text) => setRestaurantData(prev => ({ ...prev, postal_code: text }))}
                    placeholder="Postal code"
                  />
                </View>
              </View>
            </Card>

            {/* Change PIN Section */}
            <Card style={styles.section}>
              <Text style={styles.sectionTitle}>Security</Text>

              {!showPinChange ? (
                <TouchableOpacity
                  style={styles.pinChangeButton}
                  onPress={() => setShowPinChange(true)}
                >
                  <Text style={styles.pinChangeButtonText}>🔐 Change PIN</Text>
                </TouchableOpacity>
              ) : (
                <View>
                  <Input
                    label="New PIN (4-6 digits)"
                    value={newPin}
                    onChangeText={setNewPin}
                    placeholder="Enter new PIN"
                    keyboardType="numeric"
                    secureTextEntry
                    maxLength={6}
                  />

                  <Input
                    label="Confirm PIN"
                    value={confirmPin}
                    onChangeText={setConfirmPin}
                    placeholder="Confirm new PIN"
                    keyboardType="numeric"
                    secureTextEntry
                    maxLength={6}
                  />

                  <View style={styles.pinButtonRow}>
                    <TouchableOpacity
                      style={[styles.pinButton, styles.pinCancelButton]}
                      onPress={() => {
                        setShowPinChange(false);
                        setNewPin('');
                        setConfirmPin('');
                      }}
                    >
                      <Text style={styles.pinCancelButtonText}>Cancel</Text>
                    </TouchableOpacity>

                    <View style={styles.pinInfoContainer}>
                      <Text style={styles.pinInfoText}>
                        PIN will be saved when you save the profile
                      </Text>
                    </View>
                  </View>
                </View>
              )}
            </Card>

          </View>
        </ScrollView>
      </KeyboardAvoidingView>

      <FooterNavigation
        currentScreen="Settings"
        onNavigateToHome={() => {
          if (onNavigateToHome) {
            onNavigateToHome();
          }
        }}
        onNavigateToWaitingList={() => {
          if (onNavigateToHome) {
            onNavigateToHome();
          }
        }}
        onNavigateToAddPerson={() => {
          if (onNavigateToAddPerson) {
            onNavigateToAddPerson();
          }
        }}
        onNavigateToSettings={() => {
          if (onNavigateToSettings) {
            onNavigateToSettings();
          }
        }}
        onLogout={handleLogout}
      />
    </SafeAreaView>
  );
}
