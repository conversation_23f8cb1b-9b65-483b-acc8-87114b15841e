import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { Button } from '../components/Button';
import { Input } from '../components/Input';
import { Card } from '../components/Card';
import { AppHeader } from '../components/AppHeader';
import { FooterNavigation } from '../components/FooterNavigation';
import { RestaurantUserFormData } from '../types';
import { useOfflineData } from '../hooks/useOfflineData';
import { useTheme } from '../context/ThemeContext';
import { useAuth } from '../context/AuthContext';
import { apiService } from '../services/api';

interface AddUserScreenProps {
  onNavigateBack: () => void;
  onNavigateToRestaurantList?: () => void;
  onNavigateToHome?: () => void;
  onNavigateToSettings?: () => void;
}

export function AddUserScreen({ onNavigateBack, onNavigateToRestaurantList, onNavigateToHome, onNavigateToSettings }: AddUserScreenProps) {
  const [formData, setFormData] = useState<RestaurantUserFormData>({
    username: '',
    mobile_number: '',
    total_users_count: undefined,
  });
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [loading, setLoading] = useState(false);
  const [autoFillLoading, setAutoFillLoading] = useState(false);
  const [isUsernameAutoFilled, setIsUsernameAutoFilled] = useState(false);
  const { createUser, isOnline } = useOfflineData();
  const { theme } = useTheme();
  const { logout } = useAuth();

  const validateForm = (): boolean => {
    const newErrors: {[key: string]: string} = {};

    if (!formData.username.trim()) {
      newErrors.username = 'Person name is required';
    } else if (formData.username.trim().length < 2) {
      newErrors.username = 'Person name must be at least 2 characters';
    }

    if (!formData.mobile_number.trim()) {
      newErrors.mobile_number = 'Mobile number is required';
    } else if (!/^[0-9]{10,15}$/.test(formData.mobile_number.trim())) {
      newErrors.mobile_number = 'Mobile number must be 10-15 digits';
    }

    if (formData.total_users_count !== undefined && formData.total_users_count < 1) {
      newErrors.total_users_count = 'Total persons must be at least 1';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      setLoading(true);
      
      const submitData = {
        username: formData.username.trim(),
        mobile_number: formData.mobile_number.trim(),
        total_users_count: formData.total_users_count || undefined,
      };

      await createUser(submitData);

      // Auto-navigate back to dashboard without showing success alert
      onNavigateBack();
    } catch (error: any) {
      console.error('Add user error:', error);
      
      if (error.response?.data?.errors) {
        // Handle validation errors from backend
        const backendErrors = error.response.data.errors;
        const newErrors: {[key: string]: string} = {};
        
        if (backendErrors.username) {
          newErrors.username = backendErrors.username[0];
        }
        if (backendErrors.mobile_number) {
          newErrors.mobile_number = backendErrors.mobile_number[0];
        }
        if (backendErrors.total_users_count) {
          newErrors.total_users_count = backendErrors.total_users_count[0];
        }
        
        setErrors(newErrors);
      } else {
        Alert.alert('Error', 'Failed to add user. Please try again.');
      }
    } finally {
      setLoading(false);
    }
  };

  const updateFormData = (field: keyof RestaurantUserFormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: field === 'total_users_count' ? (value ? parseInt(value, 10) : undefined) : value,
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }

    // Clear auto-fill status when user manually edits username
    if (field === 'username' && isUsernameAutoFilled) {
      setIsUsernameAutoFilled(false);
    }
  };

  const handlePhoneNumberAutoFill = async (phoneNumber: string) => {
    // Only proceed if phone number is not empty and we're online
    if (!phoneNumber.trim() || !isOnline) {
      return;
    }

    try {
      setAutoFillLoading(true);

      const response = await apiService.searchUserByPhone(phoneNumber.trim());

      if (response.success && response.data.found && response.data.username) {
        // Auto-fill the username field
        setFormData(prev => ({
          ...prev,
          username: response.data.username || '',
        }));

        setIsUsernameAutoFilled(true);
        // No message needed for auto-fill
      } else {
        // No user found with this phone number
        setIsUsernameAutoFilled(false);
      }
    } catch (error) {
      console.error('Auto-fill error:', error);
      // Silently fail - don't show error to user for auto-fill
    } finally {
      setAutoFillLoading(false);
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.background,
    },
    keyboardView: {
      flex: 1,
    },
    scrollView: {
      flex: 1,
    },
    header: {
      paddingHorizontal: theme.spacing.lg,
      paddingVertical: theme.spacing.xl,
    },
    title: {
      fontSize: theme.typography.fontSize.xl,
      fontWeight: 'bold',
      color: theme.colors.text,
      marginBottom: theme.spacing.sm,
    },
    subtitle: {
      fontSize: theme.typography.fontSize.md,
      color: theme.colors.textSecondary,
    },
    buttonContainer: {
      flexDirection: 'row',
      gap: theme.spacing.md,
      marginTop: theme.spacing.lg,
    },
    cancelButton: {
      flex: 1,
    },
    submitButton: {
      flex: 1,
    },
    autoFillMessage: {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.textSecondary,
      marginTop: theme.spacing.xs,
      marginLeft: theme.spacing.sm,
      fontStyle: 'italic',
    },
    backButton: {
      backgroundColor: theme.colors.primary,
      borderRadius: 20,
      paddingHorizontal: 16,
      paddingVertical: 8,
    },
    backButtonText: {
      color: 'white',
      fontSize: 14,
      fontWeight: '600',
    },
  });

  return (
    <SafeAreaView style={styles.container}>
      <AppHeader
        title="Add New User"
        showLogo={true}
        onLogoPress={onNavigateToRestaurantList}
        rightComponent={
          <TouchableOpacity
            style={styles.backButton}
            onPress={onNavigateBack}
          >
            <Text style={styles.backButtonText}>← Back</Text>
          </TouchableOpacity>
        }
      />
      <KeyboardAvoidingView
        style={styles.keyboardView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          <View style={styles.header}>
            <Text style={styles.subtitle}>
              Enter the details for the new restaurant user
            </Text>
          </View>

          <Card>
            <Input
              label="Mobile Number"
              placeholder="Enter mobile number"
              value={formData.mobile_number}
              onChangeText={(value) => updateFormData('mobile_number', value)}
              onBlur={() => handlePhoneNumberAutoFill(formData.mobile_number)}
              error={errors.mobile_number}
              required
              keyboardType="phone-pad"
              maxLength={15}
            />

            <View>
              <Input
                label="Person Name"
                placeholder={autoFillLoading ? "Searching..." : "Enter person name"}
                value={formData.username}
                onChangeText={(value) => updateFormData('username', value)}
                error={errors.username}
                required
                autoCapitalize="words"
                editable={!autoFillLoading}
              />
              {autoFillLoading && (
                <Text style={styles.autoFillMessage}>
                  🔍 Searching for existing user...
                </Text>
              )}
            </View>

            <Input
              label="Total Persons"
              placeholder="Enter total persons (optional)"
              value={formData.total_users_count?.toString() || ''}
              onChangeText={(value) => updateFormData('total_users_count', value)}
              error={errors.total_users_count}
              keyboardType="numeric"
            />

            <View style={styles.buttonContainer}>
              <Button
                title="Cancel"
                onPress={onNavigateBack}
                variant="outline"
                style={styles.cancelButton}
              />
              <Button
                title="Add User"
                onPress={handleSubmit}
                loading={loading}
                style={styles.submitButton}
              />
            </View>
          </Card>
        </ScrollView>
      </KeyboardAvoidingView>

      <FooterNavigation
        currentScreen="WaitingList"
        onNavigateToHome={() => {
          if (onNavigateToHome) {
            onNavigateToHome();
          }
        }}
        onNavigateToAddPerson={() => {
          // Already on waiting list page
        }}
        onNavigateToSettings={() => {
          if (onNavigateToSettings) {
            onNavigateToSettings();
          }
        }}
        onLogout={() => {
          Alert.alert(
            'Logout',
            'Are you sure you want to logout?',
            [
              { text: 'Cancel', style: 'cancel' },
              {
                text: 'Logout',
                style: 'destructive',
                onPress: () => {
                  logout();
                  if (onNavigateToRestaurantList) {
                    onNavigateToRestaurantList();
                  }
                }
              },
            ]
          );
        }}
      />
    </SafeAreaView>
  );
}
