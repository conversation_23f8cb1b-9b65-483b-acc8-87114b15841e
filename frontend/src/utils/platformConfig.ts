import { Platform } from 'react-native';

/**
 * Platform-specific network configuration
 * Android emulator uses ******** to access host machine
 * Physical devices use actual IP addresses
 * iOS simulator and physical devices use actual IP addresses
 */

export class PlatformConfig {
  private static instance: PlatformConfig;
  private workingBaseURL: string | null = null;
  private connectivityTested: boolean = false;
  private isConnected: boolean = false;

  private constructor() {}

  static getInstance(): PlatformConfig {
    if (!PlatformConfig.instance) {
      PlatformConfig.instance = new PlatformConfig();
    }
    return PlatformConfig.instance;
  }

  /**
   * Get all possible base URLs for the current platform
   */
  getAllPossibleURLs(): string[] {
    if (Platform.OS === 'android') {
      return [
        'http://***********:8000/api',   // Host machine IP (prioritized)
        'http://********:8000/api',      // Android emulator
        'http://************:8000/api',  // Previous working IP
        'http://localhost:8000/api',     // Localhost
        'http://127.0.0.1:8000/api',     // Loopback
      ];
    } else {
      return [
        'http://***********:8000/api',   // Host machine IP
        'http://********:8000/api',      // Android URL as fallback
        'http://************:8000/api',  // Previous working IP
        'http://localhost:8000/api',     // Localhost
        'http://127.0.0.1:8000/api',     // Loopback
      ];
    }
  }

  /**
   * Get the appropriate base URL for the current platform
   */
  getBaseURL(): string {
    if (this.workingBaseURL) {
      return this.workingBaseURL;
    }

    // Return first URL from possible URLs as default
    return this.getAllPossibleURLs()[0];
  }

  /**
   * Set the working base URL after connectivity test
   */
  setWorkingBaseURL(url: string): void {
    this.workingBaseURL = url;
    this.connectivityTested = true;
    this.isConnected = true;
  }

  /**
   * Force set the working URL to the known good one
   */
  forceSetWorkingURL(): void {
    this.setWorkingBaseURL('http://***********:8000/api');
  }

  /**
   * Check if connectivity has been tested
   */
  isConnectivityTested(): boolean {
    return this.connectivityTested;
  }

  /**
   * Get connectivity status
   */
  getConnectivityStatus(): boolean {
    return this.isConnected;
  }

  /**
   * Reset connectivity status (for retry scenarios)
   */
  resetConnectivity(): void {
    this.connectivityTested = false;
    this.isConnected = false;
    this.workingBaseURL = null;
  }
  
  /**
   * Get fallback URLs in order of preference for current platform
   */
  getFallbackURLs(): string[] {
    const allUrls = this.getAllPossibleURLs();
    const baseUrl = this.getBaseURL();

    // Return all URLs except the current base URL
    return allUrls.filter(url => url !== baseUrl);
  }
  
  /**
   * Get the appropriate storage URL for images
   */
  getStorageURL(): string {
    const baseUrl = this.getBaseURL();
    return baseUrl.replace('/api', '/storage');
  }
  
  /**
   * Get all possible URLs to test connectivity
   */
  getAllTestURLs(): string[] {
    return this.getAllPossibleURLs();
  }
  
  /**
   * Get platform info for debugging
   */
  getPlatformInfo(): { platform: string; baseURL: string; storageURL: string } {
    return {
      platform: Platform.OS,
      baseURL: this.getBaseURL(),
      storageURL: this.getStorageURL(),
    };
  }
}

export const platformConfig = PlatformConfig.getInstance();
