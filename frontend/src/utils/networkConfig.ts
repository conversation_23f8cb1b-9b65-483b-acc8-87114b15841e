/**
 * Network configuration utility for dynamic IP detection
 */

// Get the current machine's IP address dynamically
const getCurrentIP = async (): Promise<string | null> => {
  try {
    // Try to get IP from a reliable external service
    const response = await fetch('https://api.ipify.org?format=json', {
      timeout: 5000,
    });
    const data = await response.json();
    return data.ip;
  } catch (error) {

    return null;
  }
};

// Get local network IP (for development)
const getLocalNetworkIP = (): string[] => {
  // Common local network IP patterns
  const commonIPs = [
    '***********',   // Current IP
    '************',  // Previous IP
    '***********',   // Common router IP
    '********',      // Another common pattern
    '**********',    // Private network range
  ];
  
  return commonIPs;
};

// Generate all possible backend URLs to test
export const getBackendURLs = (): string[] => {
  const port = '8000';
  const apiPath = '/api';
  
  const baseURLs = [
    // Android emulator special IP
    'http://********',

    // Current known working IP
    'http://***********',

    // Previous working IPs
    'http://************',

    // Local development
    'http://localhost',
    'http://127.0.0.1',

    // Common local network IPs
    ...getLocalNetworkIP().map(ip => `http://${ip}`),
  ];
  
  // Remove duplicates and add port + api path
  const uniqueURLs = [...new Set(baseURLs)];
  return uniqueURLs.map(url => `${url}:${port}${apiPath}`);
};

// Test connectivity to a specific URL
export const testURL = async (url: string, timeout: number = 5000): Promise<boolean> => {
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    const response = await fetch(`${url}/settings/public`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
      },
      signal: controller.signal,
    });
    
    clearTimeout(timeoutId);
    return response.ok;
  } catch (error) {
    return false;
  }
};

// Find the first working backend URL
export const findWorkingBackendURL = async (): Promise<string | null> => {
  const urls = getBackendURLs();

  for (const url of urls) {
    const isWorking = await testURL(url);

    if (isWorking) {
      return url;
    }
  }

  return null;
};

// Network configuration with automatic detection
export class NetworkConfig {
  private static instance: NetworkConfig;
  private workingURL: string | null = null;
  private lastChecked: number = 0;
  private checkInterval: number = 5 * 60 * 1000; // 5 minutes
  
  static getInstance(): NetworkConfig {
    if (!NetworkConfig.instance) {
      NetworkConfig.instance = new NetworkConfig();
    }
    return NetworkConfig.instance;
  }
  
  async getWorkingURL(): Promise<string> {
    const now = Date.now();
    
    // Check if we need to refresh the URL
    if (!this.workingURL || (now - this.lastChecked) > this.checkInterval) {
      this.workingURL = await findWorkingBackendURL();
      this.lastChecked = now;
    }

    // Fallback to default if no working URL found
    if (!this.workingURL) {
      this.workingURL = 'http://********:8000/api';
    }
    
    return this.workingURL;
  }
  
  // Force refresh the working URL
  async refresh(): Promise<string> {
    this.workingURL = null;
    this.lastChecked = 0;
    return this.getWorkingURL();
  }
}

export default NetworkConfig;
