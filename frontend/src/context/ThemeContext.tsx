import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { createTheme, Theme } from '../utils/theme';

interface ThemeContextType {
  theme: Theme;
  isDark: boolean;
  isAutoTheme: boolean;
  toggleTheme: () => void;
  setAutoTheme: (auto: boolean) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

interface ThemeProviderProps {
  children: ReactNode;
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  const [isDark, setIsDark] = useState(false);
  const [isAutoTheme, setIsAutoTheme] = useState(true);
  const theme = createTheme(isDark);

  useEffect(() => {
    loadThemePreference();
  }, []);

  useEffect(() => {
    if (isAutoTheme) {
      const checkTime = () => {
        const hour = new Date().getHours();
        // Dark theme from 6 PM (18:00) to 6 AM (06:00)
        const shouldBeDark = hour >= 18 || hour < 6;
        setIsDark(shouldBeDark);
      };

      checkTime();

      // Check every minute for time changes
      const interval = setInterval(checkTime, 60000);

      return () => clearInterval(interval);
    }
  }, [isAutoTheme]);

  const loadThemePreference = async () => {
    try {
      const [savedTheme, savedAutoTheme] = await AsyncStorage.multiGet([
        'theme_preference',
        'auto_theme_preference'
      ]);

      const autoTheme = savedAutoTheme[1] !== null ? savedAutoTheme[1] === 'true' : true;
      setIsAutoTheme(autoTheme);

      if (!autoTheme && savedTheme[1] !== null) {
        setIsDark(savedTheme[1] === 'dark');
      }
    } catch (error) {
      console.error('Failed to load theme preference:', error);
    }
  };

  const toggleTheme = async () => {
    try {
      const newTheme = !isDark;
      setIsDark(newTheme);
      setIsAutoTheme(false); // Disable auto theme when manually toggling

      await AsyncStorage.multiSet([
        ['theme_preference', newTheme ? 'dark' : 'light'],
        ['auto_theme_preference', 'false']
      ]);
    } catch (error) {
      console.error('Failed to save theme preference:', error);
    }
  };

  const setAutoThemePreference = async (auto: boolean) => {
    try {
      setIsAutoTheme(auto);
      await AsyncStorage.setItem('auto_theme_preference', auto.toString());

      if (auto) {
        // If enabling auto theme, immediately apply time-based theme
        const hour = new Date().getHours();
        const shouldBeDark = hour >= 18 || hour < 6;
        setIsDark(shouldBeDark);
      }
    } catch (error) {
      console.error('Failed to save auto theme preference:', error);
    }
  };

  const value: ThemeContextType = {
    theme,
    isDark,
    isAutoTheme,
    toggleTheme,
    setAutoTheme: setAutoThemePreference,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}
