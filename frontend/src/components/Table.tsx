import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  Linking,
  Alert,
  TouchableOpacity,
  Animated,
} from 'react-native';
import { IconButton } from './IconButton';
import { RestaurantUser } from '../types';
import { useTheme } from '../context/ThemeContext';

interface CountdownState {
  userId: number;
  timeLeft: number;
  intervalId: NodeJS.Timeout;
}

interface TableProps {
  data: RestaurantUser[];
  onEdit: (user: RestaurantUser) => void;
  onDelete: (userId: number) => void;
  onMarkAsDineIn: (userId: number) => void;
  onMarkAsWaiting: (userId: number) => void;
  onMarkAsDineInDelayed: (userId: number) => void;
}

export function Table({ data, onEdit, onDelete, onMarkAsDineIn, onMarkAsWaiting, onMarkAsDineInDelayed }: TableProps) {
  const { theme } = useTheme();
  const [countdownRows, setCountdownRows] = useState<Map<number, CountdownState>>(new Map());
  const animatedValues = useRef<Map<number, Animated.Value>>(new Map());
  const countdownTimers = useRef<Map<number, { timeLeft: number; intervalId: NodeJS.Timeout }>>(new Map());

  // Cleanup intervals on unmount
  useEffect(() => {
    return () => {
      // Clean up all timers
      countdownTimers.current.forEach((timer) => {
        clearInterval(timer.intervalId);
      });
      countdownTimers.current.clear();

      // Clean up countdown rows
      countdownRows.forEach((countdown) => {
        if (countdown.intervalId) {
          clearInterval(countdown.intervalId);
        }
      });
    };
  }, []);

  const handleCall = (phoneNumber: string) => {
    const url = `tel:${phoneNumber}`;
    Linking.canOpenURL(url)
      .then((supported) => {
        if (supported) {
          Linking.openURL(url);
        } else {
          Alert.alert('Error', 'Phone calls are not supported on this device');
        }
      })
      .catch((error) => {
        console.error('Error opening phone app:', error);
        Alert.alert('Error', 'Failed to open phone app');
      });
  };

  const startCountdown = (userId: number) => {
    // Clear any existing countdown for this user
    const existingTimer = countdownTimers.current.get(userId);
    if (existingTimer) {
      clearInterval(existingTimer.intervalId);
      countdownTimers.current.delete(userId);
    }

    const existingCountdown = countdownRows.get(userId);
    if (existingCountdown) {
      clearInterval(existingCountdown.intervalId);
    }

    // Initialize animated value if not exists
    if (!animatedValues.current.has(userId)) {
      animatedValues.current.set(userId, new Animated.Value(1));
    }

    // Start fade animation
    const animatedValue = animatedValues.current.get(userId)!;
    Animated.timing(animatedValue, {
      toValue: 0.7,
      duration: 300,
      useNativeDriver: false,
    }).start();

    // Initialize countdown with 3 seconds
    let currentTime = 3;

    // Set initial countdown state
    setCountdownRows(prev => {
      const newMap = new Map(prev);
      newMap.set(userId, { userId, timeLeft: currentTime, intervalId: null as any });
      return newMap;
    });

    const intervalId = setInterval(() => {
      currentTime -= 1;

      if (currentTime > 0) {
        // Update countdown display
        setCountdownRows(prev => {
          const newMap = new Map(prev);
          newMap.set(userId, { userId, timeLeft: currentTime, intervalId });
          return newMap;
        });
      } else {
        // Timer expired - clean up and execute action
        clearInterval(intervalId);
        countdownTimers.current.delete(userId);

        setCountdownRows(prev => {
          const newMap = new Map(prev);
          newMap.delete(userId);
          return newMap;
        });

        // Mark as dine-in using delayed function
        onMarkAsDineInDelayed(userId);
      }
    }, 1000);

    // Store timer reference
    countdownTimers.current.set(userId, { timeLeft: currentTime, intervalId });

    // Update the intervalId in the state
    setCountdownRows(prev => {
      const newMap = new Map(prev);
      newMap.set(userId, { userId, timeLeft: currentTime, intervalId });
      return newMap;
    });
  };

  const cancelCountdown = (userId: number) => {
    // Clear timer reference
    const timer = countdownTimers.current.get(userId);
    if (timer) {
      clearInterval(timer.intervalId);
      countdownTimers.current.delete(userId);
    }

    // Clear countdown state
    const countdown = countdownRows.get(userId);
    if (countdown) {
      clearInterval(countdown.intervalId);
      setCountdownRows(prev => {
        const newMap = new Map(prev);
        newMap.delete(userId);
        return newMap;
      });

      // Restore animation
      const animatedValue = animatedValues.current.get(userId);
      if (animatedValue) {
        Animated.timing(animatedValue, {
          toValue: 1,
          duration: 300,
          useNativeDriver: false,
        }).start();
      }
    }
  };

  const handleCheckboxPress = (user: RestaurantUser) => {
    if (user.status === 'dine-in') {
      onMarkAsWaiting(user.id);
    } else {
      // Start countdown instead of immediate action
      startCountdown(user.id);
    }
  };

  // Custom checkbox component
  const CustomCheckbox = ({ checked, onPress, testID }: { checked: boolean; onPress: () => void; testID?: string }) => (
    <TouchableOpacity
      style={[styles.checkbox, checked && styles.checkboxChecked]}
      onPress={onPress}
      activeOpacity={0.7}
      testID={testID}
    >
      {checked && <Text style={styles.checkmark}>✓</Text>}
    </TouchableOpacity>
  );



  const styles = StyleSheet.create({
    container: {
      backgroundColor: theme.colors.surface,
      borderRadius: theme.borderRadius.md,
      ...theme.shadows.md,
      overflow: 'hidden',
    },
    scrollView: {
      flex: 1,
    },
    table: {
      minWidth: '100%',
    },
    headerRow: {
      flexDirection: 'row',
      backgroundColor: theme.colors.primary,
      paddingVertical: theme.spacing.md,
      paddingHorizontal: theme.spacing.sm,
    },
    headerCell: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: 4,
    },
    headerText: {
      color: theme.colors.white,
      fontSize: theme.typography.fontSize.sm,
      fontWeight: '600',
      textAlign: 'center',
    },
    headerTextLeft: {
      color: theme.colors.white,
      fontSize: theme.typography.fontSize.sm,
      fontWeight: '600',
      textAlign: 'left',
    },
    row: {
      flexDirection: 'row',
      paddingVertical: theme.spacing.md,
      paddingHorizontal: theme.spacing.sm,
      borderBottomWidth: 1,
      borderBottomColor: theme.colors.border,
    },
    cell: {
      alignItems: 'center',
      justifyContent: 'center',
      paddingHorizontal: 4,
    },
    cellText: {
      color: theme.colors.text,
      fontSize: theme.typography.fontSize.sm,
    },
    cellTextCenter: {
      textAlign: 'center',
    },
    cellTextLeft: {
      textAlign: 'left',
    },
    clickableText: {
      color: theme.colors.primary,
    },
    actionCell: {
      flex: 0.8,
      alignItems: 'center',
      justifyContent: 'center',
    },
    srNoCell: {
      flex: 0.6,
      alignItems: 'center',
    },
    nameCell: {
      flex: 2,
      alignItems: 'flex-start',
      justifyContent: 'center',
    },
    totalUsersCell: {
      flex: 1,
      alignItems: 'center',
    },
    statusCell: {
      flex: 1,
      alignItems: 'center',
      justifyContent: 'center',
    },
    dineInRow: {
      backgroundColor: theme.colors.success + '20', // Light green background
    },
    actionButtons: {
      flexDirection: 'row',
      alignItems: 'center',
      gap: 8,
    },
    undoButton: {
      backgroundColor: theme.colors.warning,
      paddingHorizontal: 8,
      paddingVertical: 4,
      borderRadius: 4,
      marginRight: 4,
    },
    undoButtonText: {
      color: theme.colors.white,
      fontSize: theme.typography.fontSize.xs,
      fontWeight: '600',
    },
    checkbox: {
      width: 24,
      height: 24,
      borderWidth: 2,
      borderColor: theme.colors.textSecondary,
      borderRadius: 4,
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: theme.colors.surface,
    },
    checkboxChecked: {
      backgroundColor: theme.colors.success,
      borderColor: theme.colors.success,
    },
    checkmark: {
      color: theme.colors.white,
      fontSize: 16,
      fontWeight: 'bold',
    },
    countdownRow: {
      backgroundColor: theme.colors.warning + '30', // Light orange/yellow background
      borderLeftWidth: 4,
      borderLeftColor: theme.colors.warning,
    },
    countdownContent: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
    },
    countdownText: {
      flex: 1,
      fontSize: theme.typography.fontSize.md,
      color: theme.colors.text,
      fontWeight: '500',
    },
    countdownUndoButton: {
      backgroundColor: theme.colors.warning,
      paddingHorizontal: theme.spacing.md,
      paddingVertical: theme.spacing.sm,
      borderRadius: 6,
      minWidth: 80,
      alignItems: 'center',
    },
    countdownUndoText: {
      color: theme.colors.white,
      fontSize: theme.typography.fontSize.sm,
      fontWeight: '600',
    },
    emptyState: {
      padding: theme.spacing.xl,
      alignItems: 'center',
    },
    emptyText: {
      color: theme.colors.textSecondary,
      fontSize: theme.typography.fontSize.md,
      textAlign: 'center',
    },
  });

  if (data.length === 0) {
    return (
      <View style={styles.container}>
        <View style={styles.emptyState}>
          <Text style={styles.emptyText}>No restaurant users found</Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.scrollView}>
        <View style={styles.table}>
          {/* Header Row */}
          <View style={styles.headerRow}>
            <View style={[styles.headerCell, styles.srNoCell]}>
              <Text style={styles.headerText}>#</Text>
            </View>
            <View style={[styles.headerCell, styles.nameCell]}>
              <Text style={styles.headerTextLeft}>Name</Text>
            </View>
            <View style={[styles.headerCell, styles.totalUsersCell]}>
              <Text style={styles.headerText}>Persons</Text>
            </View>
            <View style={[styles.headerCell, styles.statusCell]}>
              <Text style={styles.headerText}>Dine-in</Text>
            </View>
            <View style={[styles.headerCell, styles.actionCell]}>
              <Text style={styles.headerText}>Call</Text>
            </View>
            <View style={[styles.headerCell, styles.actionCell]}>
              <Text style={styles.headerText}>Actions</Text>
            </View>
          </View>

          {/* Data Rows */}
          {data.map((user, index) => {
            const countdown = countdownRows.get(user.id);
            const animatedValue = animatedValues.current.get(user.id) || new Animated.Value(1);

            return (
              <Animated.View
                key={user.id}
                style={[
                  styles.row,
                  user.status === 'dine-in' && styles.dineInRow,
                  countdown && styles.countdownRow,
                  { opacity: animatedValue }
                ]}
              >
                {countdown ? (
                  // Countdown mode - show only undo button and timer
                  <View style={styles.countdownContent}>
                    <Text style={styles.countdownText}>
                      {user.username} - Moving to dine-in...
                    </Text>
                    <TouchableOpacity
                      style={styles.countdownUndoButton}
                      onPress={() => cancelCountdown(user.id)}
                    >
                      <Text style={styles.countdownUndoText}>
                        Undo ({countdown.timeLeft})
                      </Text>
                    </TouchableOpacity>
                  </View>
                ) : (
                  // Normal mode - show all cells
                  <>
                    <View style={[styles.cell, styles.srNoCell]}>
                      <Text style={[styles.cellText, styles.cellTextCenter]}>{index + 1}</Text>
                    </View>
                    <TouchableOpacity
                      style={[styles.cell, styles.nameCell]}
                      onPress={() => onEdit(user)}
                      activeOpacity={0.7}
                    >
                      <Text style={[styles.cellText, styles.cellTextLeft, styles.clickableText]} numberOfLines={1} ellipsizeMode="tail">
                        {user.username}
                      </Text>
                    </TouchableOpacity>
                    <View style={[styles.cell, styles.totalUsersCell]}>
                      <Text style={[styles.cellText, styles.cellTextCenter]}>
                        {user.total_users_count || '-'}
                      </Text>
                    </View>
                    <View style={[styles.cell, styles.statusCell]}>
                      <CustomCheckbox
                        checked={user.status === 'dine-in'}
                        testID={`checkbox-${user.id}`}
                        onPress={() => handleCheckboxPress(user)}
                      />
                    </View>
                    <View style={[styles.cell, styles.actionCell]}>
                      <IconButton
                        iconName="call"
                        onPress={() => handleCall(user.mobile_number)}
                        size={20}
                        color={theme.colors.success}
                      />
                    </View>
                    <View style={[styles.cell, styles.actionCell]}>
                      <View style={styles.actionButtons}>
                        {user.status === 'dine-in' && (
                          <TouchableOpacity
                            style={styles.undoButton}
                            onPress={() => onMarkAsWaiting(user.id)}
                          >
                            <Text style={styles.undoButtonText}>Undo</Text>
                          </TouchableOpacity>
                        )}
                        <IconButton
                          iconName="trash"
                          onPress={() => onDelete(user.id)}
                          size={20}
                          color={theme.colors.error}
                        />
                      </View>
                    </View>
                  </>
                )}
              </Animated.View>
            );
          })}
        </View>
      </ScrollView>
    </View>
  );
}
