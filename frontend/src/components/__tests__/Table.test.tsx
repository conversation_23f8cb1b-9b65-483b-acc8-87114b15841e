import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';
import { Table } from '../Table';
import { RestaurantUser } from '../../types';
import { ThemeProvider } from '../../context/ThemeContext';

const mockUser: RestaurantUser = {
  id: 1,
  username: '<PERSON>',
  mobile_number: '+**********',
  total_users_count: 2,
  status: 'waiting',
  added_by: {
    id: 1,
    name: 'Restaurant Owner',
    email: '<EMAIL>',
  },
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
};

const mockDineInUser: RestaurantUser = {
  ...mockUser,
  id: 2,
  status: 'dine-in',
};

const mockProps = {
  data: [mockUser],
  onEdit: jest.fn(),
  onDelete: jest.fn(),
  onMarkAsDineIn: jest.fn(),
  onMarkAsWaiting: jest.fn(),
};

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider>
      {component}
    </ThemeProvider>
  );
};

describe('Table Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders waiting user correctly', () => {
    const { getByText } = renderWithTheme(<Table {...mockProps} />);
    
    expect(getByText('John Doe')).toBeTruthy();
    expect(getByText('2')).toBeTruthy();
  });

  it('shows dine-in checkbox for waiting user', () => {
    const { getByTestId } = renderWithTheme(<Table {...mockProps} />);
    
    // The checkbox should be unchecked for waiting users
    const checkbox = getByTestId('checkbox-1');
    expect(checkbox).toBeTruthy();
  });

  it('calls onMarkAsDineIn when checkbox is pressed for waiting user', () => {
    const { getByTestId } = renderWithTheme(<Table {...mockProps} />);
    
    const checkbox = getByTestId('checkbox-1');
    fireEvent.press(checkbox);
    
    expect(mockProps.onMarkAsDineIn).toHaveBeenCalledWith(1);
  });

  it('shows undo button for dine-in user', () => {
    const propsWithDineInUser = {
      ...mockProps,
      data: [mockDineInUser],
    };
    
    const { getByText } = renderWithTheme(<Table {...propsWithDineInUser} />);
    
    expect(getByText('Undo')).toBeTruthy();
  });

  it('calls onMarkAsWaiting when undo button is pressed', () => {
    const propsWithDineInUser = {
      ...mockProps,
      data: [mockDineInUser],
    };
    
    const { getByText } = renderWithTheme(<Table {...propsWithDineInUser} />);
    
    const undoButton = getByText('Undo');
    fireEvent.press(undoButton);
    
    expect(mockProps.onMarkAsWaiting).toHaveBeenCalledWith(2);
  });
});
