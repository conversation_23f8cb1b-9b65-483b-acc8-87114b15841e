import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
} from 'react-native';
import { useTheme } from '../context/ThemeContext';
import { useAuth } from '../context/AuthContext';

interface FooterNavigationProps {
  currentScreen: 'Home' | 'WaitingList' | 'Settings';
  onNavigateToHome: () => void;
  onNavigateToWaitingList: () => void;
  onNavigateToAddPerson: () => void;
  onNavigateToSettings: () => void;
  onLogout: () => void;
}

export function FooterNavigation({
  currentScreen,
  onNavigateToHome,
  onNavigateToWaitingList,
  onNavigateToAddPerson,
  onNavigateToSettings,
  onLogout,
}: FooterNavigationProps) {
  const { theme } = useTheme();
  const { user } = useAuth();
  const styles = createStyles(theme);

  // Only show footer when user is logged in
  if (!user) {
    return null;
  }

  return (
    <View style={styles.footer}>
      <TouchableOpacity
        style={[styles.footerButton, currentScreen === 'Home' && styles.activeButton]}
        onPress={onNavigateToHome}
      >
        <Text style={[styles.footerButtonText, currentScreen === 'Home' && styles.activeButtonText]}>
          🏠 Home
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.footerButton, currentScreen === 'WaitingList' && styles.activeButton]}
        onPress={onNavigateToWaitingList}
      >
        <Text style={[styles.footerButtonText, currentScreen === 'WaitingList' && styles.activeButtonText]}>
          📋 Waiting List
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.footerButton, currentScreen === 'Settings' && styles.activeButton]}
        onPress={onNavigateToSettings}
      >
        <Text style={[styles.footerButtonText, currentScreen === 'Settings' && styles.activeButtonText]}>
          ⚙️ Settings
        </Text>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.logoutButton}
        onPress={onLogout}
      >
        <Text style={styles.logoutButtonText}>
          🚪 Logout
        </Text>
      </TouchableOpacity>
    </View>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  footer: {
    flexDirection: 'row',
    backgroundColor: theme.colors.surface,
    borderTopWidth: 1,
    borderTopColor: theme.colors.border,
    paddingVertical: 8,
    paddingHorizontal: 4,
    justifyContent: 'space-around',
    alignItems: 'center',
  },
  footerButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 4,
    alignItems: 'center',
    borderRadius: 8,
    marginHorizontal: 2,
  },
  activeButton: {
    backgroundColor: theme.colors.primary + '20',
  },
  footerButtonText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    textAlign: 'center',
  },
  activeButtonText: {
    color: theme.colors.primary,
    fontWeight: '600',
  },
  logoutButton: {
    flex: 1,
    paddingVertical: 8,
    paddingHorizontal: 4,
    alignItems: 'center',
    borderRadius: 8,
    marginHorizontal: 2,
    backgroundColor: theme.colors.error + '20',
  },
  logoutButtonText: {
    fontSize: 12,
    color: theme.colors.error,
    textAlign: 'center',
    fontWeight: '600',
  },
});
