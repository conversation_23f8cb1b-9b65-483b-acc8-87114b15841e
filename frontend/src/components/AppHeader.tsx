import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  StatusBar,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../context/ThemeContext';
import { settingsService } from '../services/settingsService';

interface AppHeaderProps {
  title?: string;
  showLogo?: boolean;
  showLoginButton?: boolean;
  showBackButton?: boolean;
  showOnlineStatus?: boolean;
  isOnline?: boolean;
  onLoginPress?: () => void;
  onBackPress?: () => void;
  onLogoPress?: () => void;
  rightComponent?: React.ReactNode;
}

// Get status bar height for proper padding
const getStatusBarHeight = () => {
  if (Platform.OS === 'ios') {
    return 44; // Standard iOS status bar height
  } else {
    return StatusBar.currentHeight || 24; // Android status bar height
  }
};

export function AppHeader({
  title,
  showLogo = true,
  showLoginButton = false,
  showBackButton = false,
  showOnlineStatus = false,
  isOnline = false,
  onLoginPress,
  onBackPress,
  onLogoPress,
  rightComponent
}: AppHeaderProps) {
  const { theme } = useTheme();
  const [appLogo, setAppLogo] = useState<string | null>(null);
  const [appName, setAppName] = useState<string>('Waitinglist App');

  const styles = createStyles(theme);

  useEffect(() => {
    loadAppSettings();
  }, []);

  const loadAppSettings = async () => {
    try {
      const settings = await settingsService.getPublicSettings();

      if (settings) {
        // Use app_logo (application icon) from backend
        setAppLogo(settings.app_logo);
        setAppName(settings.application_name || 'Waitinglist App');
      } else {
        setAppLogo(null);
        setAppName('Waitinglist App');
      }
    } catch (error) {
      console.error('AppHeader: Error loading app settings:', error);
      // Use fallback logo and name
      setAppLogo(null);
      setAppName('Waitinglist App');
    }
  };

  return (
    <View style={styles.header}>
      {/* Left Section - Back Button or Logo and App Name */}
      <View style={styles.leftSection}>
        {showBackButton ? (
          <TouchableOpacity
            style={styles.backButton}
            onPress={onBackPress}
          >
            <Ionicons
              name="arrow-back"
              size={24}
              color={theme.colors.text}
            />
          </TouchableOpacity>
        ) : showLogo && (
          <TouchableOpacity
            style={styles.logoContainer}
            onPress={() => {
              if (onLogoPress) {
                onLogoPress();
              }
            }}
          >
            {appLogo ? (
              <Image
                source={{ uri: appLogo }}
                style={styles.logo}
                resizeMode="contain"
                onError={(error) => {
                  console.error('Logo failed to load:', error.nativeEvent.error);
                  setAppLogo(null); // Use local fallback component
                }}
              />
            ) : (
              <View style={styles.logoFallback}>
                <Text style={styles.logoFallbackText}>🍽️</Text>
              </View>
            )}
          </TouchableOpacity>
        )}
        {!title && !showLogo && !showBackButton && (
          <Text style={styles.appName}>
            {appName}
          </Text>
        )}
        {title && (
          <Text style={styles.title}>
            {title}
          </Text>
        )}
      </View>

      {/* Right Section - Login/Logout Button and Status */}
      <View style={styles.rightSection}>
        {rightComponent}
        {showOnlineStatus && (
          <View style={[styles.statusIndicator, isOnline ? styles.statusOnline : styles.statusOffline]}>
            <Text style={[styles.statusText, isOnline ? styles.statusTextOnline : styles.statusTextOffline]}>
              {isOnline ? 'Online' : 'Offline'}
            </Text>
          </View>
        )}
        {showLoginButton && (
          <TouchableOpacity
            style={styles.loginButton}
            onPress={onLoginPress}
          >
            <Text style={styles.loginButtonText}>👤 Login</Text>
          </TouchableOpacity>
        )}

      </View>
    </View>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: getStatusBarHeight() + 12, // Add status bar height + padding
    paddingHorizontal: 16,
    paddingBottom: 12,
    backgroundColor: theme.colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    minHeight: 60 + getStatusBarHeight(), // Adjust min height
  },
  leftSection: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
  },
  rightSection: {
    flex: 1,
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  logoContainer: {
    marginRight: 12,
  },
  logo: {
    width: 120,
    height: 40,
    borderRadius: 8,
  },
  logoFallback: {
    width: 120,
    height: 40,
    backgroundColor: theme.colors.primary,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: theme.colors.border,
  },
  logoFallbackText: {
    color: '#FFFFFF',
    fontSize: 18,
    fontWeight: 'bold',
  },
  appName: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginLeft: 8,
  },
  backButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: theme.colors.surface,
    marginRight: 12,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    textAlign: 'center',
  },
  loginButton: {
    backgroundColor: theme.colors.primary,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  loginButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  logoutButton: {
    backgroundColor: theme.colors.error || '#ff4444',
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 8,
    marginLeft: 8,
  },
  logoutButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
  },
  statusIndicator: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
    alignItems: 'center',
  },
  statusOnline: {
    backgroundColor: '#4CAF50',
  },
  statusOffline: {
    backgroundColor: '#f44336',
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600',
  },
  statusTextOnline: {
    color: 'white',
  },
  statusTextOffline: {
    color: 'white',
  },
});
