import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  Alert,
  ActivityIndicator,
  TextInput,
  FlatList,
} from 'react-native';
import { useTheme } from '../context/ThemeContext';
import { locationService, LocationCoords } from '../services/locationService';
import { googlePlacesService, PlaceDetails } from '../services/googlePlacesService';

interface LocationSearchInputProps {
  onLocationSelect: (location: string, coords?: LocationCoords) => void;
  currentLocation?: string;
  placeholder?: string;
}

export function LocationSearchInput({
  onLocationSelect,
  currentLocation,
  placeholder = "Search location..."
}: LocationSearchInputProps) {
  const { theme } = useTheme();
  const [showModal, setShowModal] = useState(false);
  const [currentLocationName, setCurrentLocationName] = useState<string>('');
  const [loadingCurrentLocation, setLoadingCurrentLocation] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<PlaceDetails[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  const styles = createStyles(theme);

  useEffect(() => {
    getCurrentLocationName();
  }, []);

  // Search for places when query changes
  useEffect(() => {
    const searchPlaces = async () => {
      if (searchQuery.trim().length > 0) {
        setIsSearching(true);
        try {
          const results = await googlePlacesService.searchPlaces(searchQuery);
          setSearchResults(results);
        } catch (error) {
          console.error('Error searching places:', error);
          setSearchResults([]);
        } finally {
          setIsSearching(false);
        }
      } else {
        setSearchResults([]);
        setIsSearching(false);
      }
    };

    const timeoutId = setTimeout(searchPlaces, 300); // Debounce search
    return () => clearTimeout(timeoutId);
  }, [searchQuery]);

  const getCurrentLocationName = async () => {
    try {
      setLoadingCurrentLocation(true);
      const permission = await locationService.checkPermission();
      
      if (permission.granted) {
        const coords = await locationService.getCurrentLocation();
        if (coords) {
          const address = await locationService.getAddressFromCoords(coords);
          if (address) {
            // Extract city name from address
            const addressParts = address.split(', ');
            const cityName = addressParts.length >= 2 ? addressParts[1] : addressParts[0];
            setCurrentLocationName(cityName || 'Current Location');
          } else {
            setCurrentLocationName('Current Location');
          }
        }
      }
    } catch (error) {
      console.error('Error getting current location name:', error);
      setCurrentLocationName('Current Location');
    } finally {
      setLoadingCurrentLocation(false);
    }
  };

  const handleUseCurrentLocation = async () => {
    try {
      const permission = await locationService.requestPermission();
      
      if (permission.granted) {
        const coords = await locationService.getCurrentLocation();
        if (coords) {
          const address = await locationService.getAddressFromCoords(coords);
          const cityName = currentLocationName || 'Current Location';
          onLocationSelect(cityName, coords);
          setShowModal(false);
        } else {
          Alert.alert('Error', 'Unable to get current location');
        }
      } else {
        Alert.alert(
          'Location Permission',
          'Location permission is required to use current location'
        );
      }
    } catch (error) {
      console.error('Error using current location:', error);
      Alert.alert('Error', 'Failed to get current location');
    }
  };

  const handlePlaceSelect = async (place: PlaceDetails) => {
    try {
      setIsSearching(true);

      // Get coordinates for the selected place
      let coords: LocationCoords | null = null;
      if (place.place_id.startsWith('hardcoded_')) {
        // For hardcoded cities, we don't have coordinates
        coords = null;
      } else {
        coords = await googlePlacesService.getPlaceDetails(place.place_id);
      }

      onLocationSelect(place.description, coords || undefined);
      setShowModal(false);
      setSearchQuery('');
      setSearchResults([]);
    } catch (error) {
      console.error('Error selecting place:', error);
      Alert.alert('Error', 'Failed to select location');
    } finally {
      setIsSearching(false);
    }
  };

  const renderLocationButton = () => (
    <TouchableOpacity
      style={styles.locationButton}
      onPress={() => setShowModal(true)}
    >
      <Text style={styles.locationButtonText}>
        📍 {currentLocation || currentLocationName || 'Select Location'}
      </Text>
    </TouchableOpacity>
  );

  const renderLocationModal = () => (
    <Modal
      visible={showModal}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={() => setShowModal(false)}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalHeader}>
          <Text style={styles.modalTitle}>Select Location</Text>
          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => setShowModal(false)}
          >
            <Text style={styles.closeButtonText}>✕</Text>
          </TouchableOpacity>
        </View>

        {/* Current Location Option */}
        <TouchableOpacity
          style={styles.currentLocationOption}
          onPress={handleUseCurrentLocation}
          disabled={loadingCurrentLocation}
        >
          <View style={styles.currentLocationContent}>
            <Text style={styles.currentLocationIcon}>📍</Text>
            <View style={styles.currentLocationTextContainer}>
              <Text style={styles.currentLocationTitle}>Use Current Location</Text>
              <Text style={styles.currentLocationSubtitle}>
                {loadingCurrentLocation ? 'Getting location...' : currentLocationName}
              </Text>
            </View>
            {loadingCurrentLocation && (
              <ActivityIndicator size="small" color={theme.colors.primary} />
            )}
          </View>
        </TouchableOpacity>

        <View style={styles.divider} />

        {/* City Search */}
        <View style={styles.searchContainer}>
          <Text style={styles.searchLabel}>Search for a location</Text>
          <TextInput
            style={styles.searchInput}
            placeholder="Type a city or place name..."
            placeholderTextColor={theme.colors.textSecondary}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />

          {isSearching && (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="small" color={theme.colors.primary} />
              <Text style={styles.loadingText}>Searching...</Text>
            </View>
          )}

          <FlatList
            data={searchResults}
            keyExtractor={(item) => item.place_id}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={styles.cityItem}
                onPress={() => handlePlaceSelect(item)}
              >
                <Text style={styles.cityItemText}>{item.structured_formatting.main_text}</Text>
                {item.structured_formatting.secondary_text && (
                  <Text style={styles.cityItemSubtext}>{item.structured_formatting.secondary_text}</Text>
                )}
              </TouchableOpacity>
            )}
            style={styles.cityList}
            showsVerticalScrollIndicator={false}
            ListEmptyComponent={
              searchQuery.length > 0 && !isSearching ? (
                <Text style={styles.noResultsText}>No locations found</Text>
              ) : null
            }
          />
        </View>
      </View>
    </Modal>
  );

  return (
    <>
      {renderLocationButton()}
      {renderLocationModal()}
    </>
  );
}

const createStyles = (theme: any) => StyleSheet.create({
  locationButton: {
    flex: 0.3,
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: theme.colors.border,
    justifyContent: 'center',
    alignItems: 'center',
  },
  locationButtonText: {
    fontSize: 12,
    color: theme.colors.text,
    fontWeight: '500',
    textAlign: 'center',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: theme.colors.text,
  },
  closeButton: {
    padding: 8,
  },
  closeButtonText: {
    fontSize: 18,
    color: theme.colors.textSecondary,
  },
  currentLocationOption: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  currentLocationContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  currentLocationIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  currentLocationTextContainer: {
    flex: 1,
  },
  currentLocationTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
  },
  currentLocationSubtitle: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  divider: {
    height: 8,
    backgroundColor: theme.colors.border,
  },
  searchContainer: {
    flex: 1,
    padding: 16,
  },
  searchLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: theme.colors.text,
    marginBottom: 12,
  },
  searchInput: {
    backgroundColor: theme.colors.surface,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: theme.colors.border,
    fontSize: 16,
    color: theme.colors.text,
    paddingHorizontal: 12,
    paddingVertical: 12,
    marginBottom: 12,
  },
  cityList: {
    maxHeight: 300,
  },
  cityItem: {
    backgroundColor: theme.colors.surface,
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
    borderRadius: 4,
    marginBottom: 2,
  },
  cityItemText: {
    fontSize: 14,
    color: theme.colors.text,
    fontWeight: '500',
  },
  cityItemSubtext: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginTop: 2,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  loadingText: {
    marginLeft: 8,
    fontSize: 14,
    color: theme.colors.textSecondary,
  },
  noResultsText: {
    textAlign: 'center',
    fontSize: 14,
    color: theme.colors.textSecondary,
    padding: 16,
    fontStyle: 'italic',
  },
});
