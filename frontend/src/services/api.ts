import axios, { AxiosInstance, AxiosResponse } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { ApiResponse, User, RestaurantUser, RestaurantUserFormData } from '../types';
import { NetworkConfig } from '../utils/networkConfig';
import { platformConfig } from '../utils/platformConfig';

// Use platform-specific configuration
const BASE_URL = platformConfig.getBaseURL();
const FALLBACK_URLS = platformConfig.getFallbackURLs();



class ApiService {
  private api: AxiosInstance;
  private currentBaseUrl: string = BASE_URL;
  private networkConfig: NetworkConfig;

  constructor() {
    this.networkConfig = NetworkConfig.getInstance();
    this.currentBaseUrl = BASE_URL;

    this.api = axios.create({
      baseURL: BASE_URL,
      timeout: 10000, // Reduced timeout for faster failure detection
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      async (config) => {
        const token = await AsyncStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        console.error('API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor for error handling
    this.api.interceptors.response.use(
      (response) => {
        return response;
      },
      async (error) => {
        console.error(`API Error: ${error.message}`, {
          url: error.config?.url,
          method: error.config?.method,
          status: error.response?.status,
          data: error.response?.data,
        });

        if (error.response?.status === 401) {
          // Token expired or invalid, clear storage
          await AsyncStorage.multiRemove(['auth_token', 'user_data']);
        }
        return Promise.reject(error);
      }
    );
  }

  // Network connectivity methods
  async testConnectivity(): Promise<boolean> {
    // Return cached result if already tested
    if (platformConfig.isConnectivityTested()) {
      return platformConfig.getConnectivityStatus();
    }

    const testUrls = platformConfig.getAllTestURLs();

    for (const url of testUrls) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 3000);

        const response = await fetch(`${url}/settings/public`, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          },
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (response.ok) {
          // Update platform config with working URL
          platformConfig.setWorkingBaseURL(url);

          // Update base URL if different from current
          if (url !== this.currentBaseUrl) {
            this.currentBaseUrl = url;
            this.api.defaults.baseURL = url;
          }
          return true;
        }
      } catch (error) {
        // Continue to next URL silently
        continue;
      }
    }

    // Mark as tested even if failed
    platformConfig.resetConnectivity();
    return false;
  }

  async testAndSwitchToWorkingUrl(): Promise<boolean> {
    try {
      const workingUrl = await this.networkConfig.getWorkingURL();

      if (workingUrl && workingUrl !== this.currentBaseUrl) {
        this.currentBaseUrl = workingUrl;
        this.api.defaults.baseURL = workingUrl;
      }

      return workingUrl !== null;
    } catch (error) {
      console.error('Failed to get working URL:', error);
      return false;
    }
  }

  // Generic GET method with retry logic
  async get<T = any>(url: string, retries: number = 2): Promise<ApiResponse<T>> {
    // Test connectivity if not already tested
    if (!platformConfig.isConnectivityTested()) {
      await this.testConnectivity();
    }

    for (let i = 0; i <= retries; i++) {
      try {
        const response: AxiosResponse<ApiResponse<T>> = await this.api.get(url);
        return response.data;
      } catch (error) {
        // If first attempt fails, try to find working URL
        if (i === 0) {
          const isConnected = await this.testConnectivity();
          if (isConnected && this.currentBaseUrl !== this.api.defaults.baseURL) {
            // URL changed, retry with new URL
            continue;
          }
        }

        // Only log error on final retry
        if (i === retries) {
          console.error(`API request failed after ${retries + 1} attempts for ${url}`);
        }

        if (i < retries) {
          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
        } else {
          throw error;
        }
      }
    }

    throw new Error('Max retries exceeded');
  }

  // Authentication methods
  async googleAuth(accessToken: string): Promise<ApiResponse<{ user: User; token: string }>> {
    const response: AxiosResponse<ApiResponse<{ user: User; token: string }>> = 
      await this.api.post('/auth/google', { access_token: accessToken });
    return response.data;
  }

  async getUser(): Promise<ApiResponse<User>> {
    const response: AxiosResponse<ApiResponse<User>> = await this.api.get('/auth/user');
    return response.data;
  }

  async logout(): Promise<ApiResponse<null>> {
    const response: AxiosResponse<ApiResponse<null>> = await this.api.post('/auth/logout');
    return response.data;
  }

  // Restaurant Users methods
  async getRestaurantUsers(params?: {
    page?: number;
    per_page?: number;
    search?: string;
    sort_by?: string;
    sort_order?: 'asc' | 'desc';
    status?: 'waiting' | 'dine-in' | 'all';
  }): Promise<ApiResponse<RestaurantUser[]>> {
    const response: AxiosResponse<ApiResponse<RestaurantUser[]>> = 
      await this.api.get('/restaurant-users', { params });
    return response.data;
  }

  async createRestaurantUser(data: RestaurantUserFormData): Promise<ApiResponse<RestaurantUser>> {
    const response: AxiosResponse<ApiResponse<RestaurantUser>> = 
      await this.api.post('/restaurant-users', data);
    return response.data;
  }

  async getRestaurantUser(id: number): Promise<ApiResponse<RestaurantUser>> {
    const response: AxiosResponse<ApiResponse<RestaurantUser>> = 
      await this.api.get(`/restaurant-users/${id}`);
    return response.data;
  }

  async updateRestaurantUser(id: number, data: Partial<RestaurantUserFormData>): Promise<ApiResponse<RestaurantUser>> {
    const response: AxiosResponse<ApiResponse<RestaurantUser>> = 
      await this.api.put(`/restaurant-users/${id}`, data);
    return response.data;
  }

  async deleteRestaurantUser(id: number): Promise<ApiResponse<null>> {
    const response: AxiosResponse<ApiResponse<null>> =
      await this.api.delete(`/restaurant-users/${id}`);
    return response.data;
  }

  // Status management methods
  async markUserAsDineIn(id: number): Promise<ApiResponse<RestaurantUser>> {
    const response: AxiosResponse<ApiResponse<RestaurantUser>> =
      await this.api.post(`/restaurant-users/${id}/mark-dine-in`);
    return response.data;
  }

  async markUserAsWaiting(id: number): Promise<ApiResponse<RestaurantUser>> {
    const response: AxiosResponse<ApiResponse<RestaurantUser>> =
      await this.api.post(`/restaurant-users/${id}/mark-waiting`);
    return response.data;
  }

  async getWaitingCount(): Promise<ApiResponse<{ waiting_count: number }>> {
    const response: AxiosResponse<ApiResponse<{ waiting_count: number }>> =
      await this.api.get('/restaurant-users/waiting-count');
    return response.data;
  }

  // Admin authentication methods
  async adminLogin(email: string, password: string): Promise<ApiResponse<{ user: any; token: string }>> {
    const response: AxiosResponse<ApiResponse<{ user: any; token: string }>> =
      await this.api.post('/auth/admin-login', { email, password });
    return response.data;
  }

  async pinLogin(email: string, pin: string): Promise<ApiResponse<{ user: any; token: string }>> {
    const response: AxiosResponse<ApiResponse<{ user: any; token: string }>> =
      await this.api.post('/auth/pin-login', { email, pin });
    return response.data;
  }

  async setPin(pin: string): Promise<ApiResponse<{ user: any }>> {
    const response: AxiosResponse<ApiResponse<{ user: any }>> =
      await this.api.post('/auth/set-pin', { pin });
    return response.data;
  }

  // Admin dashboard methods
  async getRestaurantOwners(params?: {
    page?: number;
    per_page?: number;
    search?: string;
    sort_by?: string;
    sort_order?: 'asc' | 'desc';
  }): Promise<ApiResponse<any[]>> {
    const response: AxiosResponse<ApiResponse<any[]>> =
      await this.api.get('/admin/restaurant-owners', { params });
    return response.data;
  }

  async getRestaurantUsersByOwner(ownerId: number, params?: {
    page?: number;
    per_page?: number;
    search?: string;
    sort_by?: string;
    sort_order?: 'asc' | 'desc';
  }): Promise<ApiResponse<any[]>> {
    const response: AxiosResponse<ApiResponse<any[]>> =
      await this.api.get(`/admin/restaurant-owners/${ownerId}/users`, { params });
    return response.data;
  }

  async getDashboardStats(): Promise<ApiResponse<any>> {
    const response: AxiosResponse<ApiResponse<any>> =
      await this.api.get('/admin/dashboard-stats');
    return response.data;
  }

  // Profile management methods
  async updateProfile(data: { name: string; profile_picture?: string }): Promise<ApiResponse<{ user: any }>> {
    const response: AxiosResponse<ApiResponse<{ user: any }>> =
      await this.api.put('/auth/profile', data);
    return response.data;
  }

  // Generic HTTP methods with retry logic
  async getGeneric(url: string, params?: any, retries: number = 2): Promise<any> {
    for (let i = 0; i <= retries; i++) {
      try {
        const response = await this.api.get(url, { params });
        return response.data;
      } catch (error) {
        // Only log error on final retry
        if (i === retries) {
          console.error(`API request failed after ${retries + 1} attempts for ${url}`);
        }
        if (i === retries) {
          throw error;
        }
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
      }
    }
  }

  async post(url: string, data?: any): Promise<any> {
    const response = await this.api.post(url, data);
    return response.data;
  }

  async put(url: string, data?: any): Promise<any> {
    const response = await this.api.put(url, data);
    return response.data;
  }

  async delete(url: string): Promise<any> {
    const response = await this.api.delete(url);
    return response.data;
  }

  // Auto-fill functionality
  async searchUserByPhone(phone: string): Promise<ApiResponse<{
    username: string | null;
    phone_number: string;
    found: boolean;
    created_at?: string;
  }>> {
    const response: AxiosResponse<ApiResponse<{
      username: string | null;
      phone_number: string;
      found: boolean;
      created_at?: string;
    }>> = await this.api.get(`/restaurant-users/search/by-phone/${encodeURIComponent(phone)}`);
    return response.data;
  }

  // Restaurant profile management methods
  async getRestaurantProfile(): Promise<ApiResponse<any>> {
    const response: AxiosResponse<ApiResponse<any>> = await this.api.get('/restaurants/my-restaurant');
    return response.data;
  }

  async getMyRestaurant(): Promise<ApiResponse<any>> {
    const response: AxiosResponse<ApiResponse<any>> = await this.api.get('/restaurants/my-restaurant');
    return response.data;
  }

  async updateRestaurantProfile(formData: FormData): Promise<ApiResponse<any>> {
    const response: AxiosResponse<ApiResponse<any>> = await this.api.post('/restaurants/my-restaurant', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  async updateRestaurantProfileJson(data: any): Promise<ApiResponse<any>> {
    const response: AxiosResponse<ApiResponse<any>> = await this.api.post('/restaurants/my-restaurant', data);
    return response.data;
  }

  // Public restaurant methods
  async getPublicRestaurants(params?: { latitude?: number; longitude?: number; radius?: number; search?: string }): Promise<ApiResponse<any>> {
    let queryParams = '';
    if (params) {
      const searchParams = new URLSearchParams();
      if (params.latitude && params.longitude) {
        searchParams.append('latitude', params.latitude.toString());
        searchParams.append('longitude', params.longitude.toString());
        if (params.radius) {
          searchParams.append('radius', params.radius.toString());
        }
      }
      if (params.search) {
        searchParams.append('search', params.search);
      }
      queryParams = searchParams.toString() ? `?${searchParams.toString()}` : '';
    }

    const response: AxiosResponse<ApiResponse<any>> = await this.api.get(`/restaurants/public${queryParams}`);
    return response.data;
  }
}

export const apiService = new ApiService();
