import NetInfo from '@react-native-community/netinfo';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { apiService } from './api';
import { storageService, PendingAction } from './storage';
import { RestaurantUser } from '../types';

class SyncService {
  private static instance: SyncService;
  private isOnline: boolean = true;
  private syncInProgress: boolean = false;
  private listeners: Array<(isOnline: boolean) => void> = [];
  private realTimeListeners: Array<(changedRestaurantIds?: number[]) => void> = [];
  private realTimeInterval: NodeJS.Timeout | null = null;
  private isRealTimeActive: boolean = false;
  private lastRestaurantData: any[] = [];

  public static getInstance(): SyncService {
    if (!SyncService.instance) {
      SyncService.instance = new SyncService();
    }
    return SyncService.instance;
  }

  constructor() {
    this.initializeNetworkListener();
    this.initializeRestaurantData();
  }

  private async initializeRestaurantData() {
    try {
      const response = await apiService.getPublicRestaurants();
      if (response.success && response.data) {
        this.lastRestaurantData = response.data.data || [];
        console.log('Initialized restaurant data for change detection');
      }
    } catch (error) {
      console.log('Failed to initialize restaurant data:', error);
    }
  }

  private initializeNetworkListener() {
    NetInfo.addEventListener(state => {
      const wasOnline = this.isOnline;
      this.isOnline = state.isConnected ?? false;
      
      // Update sync status
      storageService.updateSyncStatus({ isOnline: this.isOnline });
      
      // Notify listeners
      this.listeners.forEach(listener => listener(this.isOnline));
      
      // If we just came online, sync pending actions
      if (!wasOnline && this.isOnline) {
        this.syncPendingActions();
      }
    });
  }

  addNetworkListener(listener: (isOnline: boolean) => void) {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  addRealTimeListener(listener: (changedRestaurantIds?: number[]) => void) {
    this.realTimeListeners.push(listener);
    return () => {
      this.realTimeListeners = this.realTimeListeners.filter(l => l !== listener);
    };
  }

  private notifyRealTimeListeners(changedRestaurantIds?: number[]) {
    this.realTimeListeners.forEach(listener => listener(changedRestaurantIds));
  }

  private getChangedRestaurants(newData: any[]): number[] {
    const changedIds: number[] = [];

    // If no previous data, consider all restaurants changed
    if (this.lastRestaurantData.length === 0) {
      return newData.map(r => r.id);
    }

    // If different number of restaurants, consider all changed
    if (this.lastRestaurantData.length !== newData.length) {
      return newData.map(r => r.id);
    }

    // Check which restaurants' waiting counts changed
    for (const newRestaurant of newData) {
      const oldRestaurant = this.lastRestaurantData.find(r => r.id === newRestaurant.id);
      if (!oldRestaurant || oldRestaurant.current_waiting_count !== newRestaurant.current_waiting_count) {
        console.log(`Restaurant ${newRestaurant.name} count changed: ${oldRestaurant?.current_waiting_count || 'new'} → ${newRestaurant.current_waiting_count}`);
        changedIds.push(newRestaurant.id);
      }
    }

    return changedIds;
  }

  startRealTimeUpdates() {
    if (this.isRealTimeActive) {
      return;
    }

    this.isRealTimeActive = true;
    this.realTimeInterval = setInterval(() => {
      if (this.isOnline && !this.syncInProgress) {
        this.syncData().then(() => {
          // Notify all real-time listeners (general sync, no specific restaurants)
          this.realTimeListeners.forEach(listener => listener());
        }).catch(error => {
          // Reduce error logging frequency to prevent spam
          if (Math.random() < 0.1) { // Only log 10% of errors
            console.error('Real-time sync failed:', error.message || error);
          }
        });
      }
    }, 5000); // Poll every 5 seconds
  }

  stopRealTimeUpdates() {
    if (this.realTimeInterval) {
      clearInterval(this.realTimeInterval);
      this.realTimeInterval = null;
    }
    this.isRealTimeActive = false;
  }

  getNetworkStatus(): boolean {
    return this.isOnline;
  }

  async syncData(): Promise<void> {
    if (!this.isOnline || this.syncInProgress) {
      return;
    }

    try {
      this.syncInProgress = true;
      
      // Sync pending actions first
      await this.syncPendingActions();
      
      // Then fetch latest data from server
      await this.fetchLatestData();
      
      // Update last sync time
      await storageService.updateSyncStatus({ lastSyncTime: Date.now() });
      
    } catch (error) {
      console.error('Sync failed:', error);
    } finally {
      this.syncInProgress = false;
    }
  }

  private async syncPendingActions(): Promise<void> {
    // Check if user is authenticated before syncing
    const token = await AsyncStorage.getItem('auth_token');
    if (!token) {
      console.log('Skipping pending actions sync - user not authenticated');
      return;
    }

    const pendingActions = await storageService.getPendingActions();

    for (const action of pendingActions) {
      try {
        await this.processPendingAction(action);
        await storageService.removePendingAction(action.id);
      } catch (error) {
        console.error(`Failed to sync action ${action.id}:`, error);

        // Handle 401 errors by clearing auth data
        if ((error as any).response?.status === 401) {
          console.log('Authentication expired during sync, clearing local auth data');
          await AsyncStorage.multiRemove(['auth_token', 'user_data']);
          break; // Stop syncing if auth is invalid
        }

        // Handle 422 validation errors by removing the action immediately
        if ((error as any).response?.status === 422) {
          console.warn(`Validation error for action ${action.id}, removing invalid action:`, (error as any).response?.data);
          await storageService.removePendingAction(action.id);
          continue; // Skip retry logic for validation errors
        }

        // Increment retry count
        await storageService.incrementRetryCount(action.id);

        // Remove action if it has failed too many times
        if (action.retryCount >= 3) {
          console.warn(`Removing action ${action.id} after 3 failed attempts`);
          await storageService.removePendingAction(action.id);
        }
      }
    }
  }

  private async processPendingAction(action: PendingAction): Promise<void> {
    switch (action.type) {
      case 'CREATE':
        const createResponse = await apiService.createRestaurantUser(action.data);
        if (createResponse.success) {
          // Update local storage with server response
          await storageService.updateRestaurantUser(createResponse.data);
        }
        break;
      case 'UPDATE':
        // Handle status updates specifically
        if (action.data.status) {
          if (action.data.status === 'dine-in') {
            const dineInResponse = await apiService.markUserAsDineIn(action.data.id);
            if (dineInResponse.success) {
              await storageService.updateRestaurantUser(dineInResponse.data);
            }
          } else if (action.data.status === 'waiting') {
            const waitingResponse = await apiService.markUserAsWaiting(action.data.id);
            if (waitingResponse.success) {
              await storageService.updateRestaurantUser(waitingResponse.data);
            }
          }
        } else {
          // Regular update
          const updateResponse = await apiService.updateRestaurantUser(action.data.id, action.data);
          if (updateResponse.success) {
            // Update local storage with server response
            await storageService.updateRestaurantUser(updateResponse.data);
          }
        }
        break;
      case 'DELETE':
        await apiService.deleteRestaurantUser(action.data.id);
        // Local deletion already happened, no need to update storage
        break;
      default:
        throw new Error(`Unknown action type: ${action.type}`);
    }
  }

  private async fetchLatestData(): Promise<void> {
    try {
      // Check if user is authenticated before making API calls
      const token = await AsyncStorage.getItem('auth_token');
      if (!token) {
        console.log('Skipping data fetch - user not authenticated');
        return;
      }

      // Don't overwrite local data if there are pending actions
      const pendingActions = await storageService.getPendingActions();
      if (pendingActions.length > 0) {
        console.log('Skipping data fetch - pending actions exist');
        return;
      }

      const response = await apiService.getRestaurantUsers({
        per_page: 100, // Fetch more data for offline use
        sort_by: 'created_at',
        sort_order: 'desc',
        status: 'waiting', // Only fetch waiting users by default
      });

      if (response.success) {
        await storageService.saveRestaurantUsers(response.data);
      }

      // Also fetch updated restaurant data to get current waiting counts
      // This is crucial for real-time restaurant list updates
      try {
        const restaurantsResponse = await apiService.getPublicRestaurants();
        if (restaurantsResponse.success && restaurantsResponse.data) {
          const newRestaurantData = restaurantsResponse.data.data || [];

          // Check which restaurants actually changed
          const changedRestaurantIds = this.getChangedRestaurants(newRestaurantData);

          if (changedRestaurantIds.length > 0) {
            console.log(`Restaurant data changed for ${changedRestaurantIds.length} restaurants - triggering real-time listeners`);
            this.lastRestaurantData = newRestaurantData;
            this.notifyRealTimeListeners(changedRestaurantIds);
          } else {
            console.log('No restaurant data changes - skipping real-time notification');
          }
        }
      } catch (restaurantError) {
        // Reduce error logging frequency
        if (Math.random() < 0.1) {
          console.log('Failed to fetch restaurant data during sync:', (restaurantError as any).message || restaurantError);
        }
        // Don't throw error for restaurant data fetch failure
      }
    } catch (error) {
      // Handle 401 errors gracefully
      if ((error as any).response?.status === 401) {
        console.log('Authentication expired, clearing local auth data');
        await AsyncStorage.multiRemove(['auth_token', 'user_data']);
      } else {
        // Reduce error logging frequency for network errors
        if (Math.random() < 0.1) {
          console.error('Failed to fetch latest data:', (error as any).message || error);
        }
      }
    }
  }

  // Offline-first CRUD operations
  async createRestaurantUserOffline(data: any): Promise<RestaurantUser> {
    // Create a temporary user object for immediate UI update
    const tempUser: RestaurantUser = {
      id: Date.now(), // Temporary ID
      username: data.username,
      mobile_number: data.mobile_number,
      total_users_count: data.total_users_count,
      added_by: {
        id: 0, // Will be filled by server
        name: 'You',
        email: '',
      },
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };

    // Add to local storage immediately
    await storageService.addRestaurantUser(tempUser);

    if (this.isOnline) {
      try {
        // Try to sync immediately if online
        const response = await apiService.createRestaurantUser(data);
        if (response.success) {
          // Update with real data from server
          await storageService.updateRestaurantUser(response.data);
          return response.data;
        }
      } catch (error) {
        console.error('Failed to create user online, queuing for later:', error);
      }
    }

    // Queue for later sync if offline or online sync failed
    await storageService.addPendingAction({
      type: 'CREATE',
      data,
    });

    return tempUser;
  }

  async updateRestaurantUserOffline(id: number, data: any): Promise<void> {
    console.log('updateRestaurantUserOffline called with:', { id, data });

    // Update local storage immediately
    const users = await storageService.getRestaurantUsers();
    const userIndex = users.findIndex(u => u.id === id);

    if (userIndex !== -1) {
      const updatedUser = { ...users[userIndex], ...data, updated_at: new Date().toISOString() };
      console.log('Updating user in local storage:', updatedUser);
      await storageService.updateRestaurantUser(updatedUser);
    } else {
      console.log('User not found in local storage:', id);
    }

    if (this.isOnline) {
      try {
        console.log('Attempting to update user online:', { id, data });
        // Try to sync immediately if online
        const response = await apiService.updateRestaurantUser(id, data);
        console.log('Update user API response:', response);
        if (response.success) {
          // Update with server response to ensure consistency
          const serverUser = response.data;
          console.log('Updating user with server response:', serverUser);
          await storageService.updateRestaurantUser(serverUser);
        }
        return;
      } catch (error) {
        console.error('Failed to update user online, queuing for later:', error);
      }
    }

    // Queue for later sync if offline or online sync failed
    await storageService.addPendingAction({
      type: 'UPDATE',
      data: { id, ...data },
    });
  }

  async deleteRestaurantUserOffline(id: number): Promise<void> {
    // Remove from local storage immediately
    await storageService.deleteRestaurantUser(id);

    if (this.isOnline) {
      try {
        // Try to sync immediately if online
        await apiService.deleteRestaurantUser(id);
        return;
      } catch (error) {
        console.error('Failed to delete user online, queuing for later:', error);
      }
    }

    // Queue for later sync if offline or online sync failed
    await storageService.addPendingAction({
      type: 'DELETE',
      data: { id },
    });
  }

  async getRestaurantUsersOffline(searchQuery?: string): Promise<RestaurantUser[]> {
    if (searchQuery) {
      return await storageService.searchRestaurantUsers(searchQuery);
    }
    return await storageService.getRestaurantUsers();
  }

  async getRestaurantUsersByStatus(status: 'waiting' | 'dine-in' | 'all' = 'waiting'): Promise<RestaurantUser[]> {
    const users = await storageService.getRestaurantUsers();

    if (status === 'all') {
      return users;
    }

    return users.filter(user => user.status === status);
  }

  // Status management methods
  async markUserAsDineInOffline(id: number): Promise<void> {
    // Update local storage immediately
    const users = await storageService.getRestaurantUsers();
    const userIndex = users.findIndex(u => u.id === id);
    if (userIndex !== -1) {
      users[userIndex].status = 'dine-in';
      await storageService.saveRestaurantUsers(users);
    }

    if (this.isOnline) {
      try {
        // Try to sync immediately if online
        await apiService.markUserAsDineIn(id);
        return;
      } catch (error) {
        console.error('Failed to mark user as dine-in online, queuing for later:', error);
      }
    }

    // Queue for later sync if offline or online sync failed
    await storageService.addPendingAction({
      type: 'UPDATE',
      data: { id, status: 'dine-in' },
    });
  }

  async markUserAsWaitingOffline(id: number): Promise<void> {
    // Update local storage immediately
    const users = await storageService.getRestaurantUsers();
    const userIndex = users.findIndex(u => u.id === id);
    if (userIndex !== -1) {
      users[userIndex].status = 'waiting';
      await storageService.saveRestaurantUsers(users);
    }

    if (this.isOnline) {
      try {
        // Try to sync immediately if online
        await apiService.markUserAsWaiting(id);
        return;
      } catch (error) {
        console.error('Failed to mark user as waiting online, queuing for later:', error);
      }
    }

    // Queue for later sync if offline or online sync failed
    await storageService.addPendingAction({
      type: 'UPDATE',
      data: { id, status: 'waiting' },
    });
  }

  isSyncInProgress(): boolean {
    return this.syncInProgress;
  }

  async getPendingActionsCount(): Promise<number> {
    const actions = await storageService.getPendingActions();
    return actions.length;
  }
}

export const syncService = SyncService.getInstance();
