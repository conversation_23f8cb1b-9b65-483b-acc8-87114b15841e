import * as Location from 'expo-location';

export interface LocationCoords {
  latitude: number;
  longitude: number;
}

export interface LocationPermissionStatus {
  granted: boolean;
  canAskAgain: boolean;
}

class LocationService {
  private currentLocation: LocationCoords | null = null;
  private permissionStatus: LocationPermissionStatus | null = null;

  /**
   * Request location permission from the user
   */
  async requestPermission(): Promise<LocationPermissionStatus> {
    try {
      const { status, canAskAgain } = await Location.requestForegroundPermissionsAsync();
      
      this.permissionStatus = {
        granted: status === 'granted',
        canAskAgain,
      };

      return this.permissionStatus;
    } catch (error) {
      console.error('Error requesting location permission:', error);
      this.permissionStatus = {
        granted: false,
        canAskAgain: false,
      };
      return this.permissionStatus;
    }
  }

  /**
   * Check current location permission status
   */
  async checkPermission(): Promise<LocationPermissionStatus> {
    try {
      const { status, canAskAgain } = await Location.getForegroundPermissionsAsync();
      
      this.permissionStatus = {
        granted: status === 'granted',
        canAskAgain,
      };

      return this.permissionStatus;
    } catch (error) {
      console.error('Error checking location permission:', error);
      this.permissionStatus = {
        granted: false,
        canAskAgain: false,
      };
      return this.permissionStatus;
    }
  }

  /**
   * Get current location
   */
  async getCurrentLocation(): Promise<LocationCoords | null> {
    try {
      const permission = await this.checkPermission();
      
      if (!permission.granted) {
        console.warn('Location permission not granted');
        return null;
      }

      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.Balanced,
        timeInterval: 10000,
        distanceInterval: 100,
      });

      this.currentLocation = {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
      };

      return this.currentLocation;
    } catch (error) {
      console.error('Error getting current location:', error);
      return null;
    }
  }

  /**
   * Get cached location (if available)
   */
  getCachedLocation(): LocationCoords | null {
    return this.currentLocation;
  }

  /**
   * Calculate distance between two coordinates using Haversine formula
   */
  calculateDistance(
    coord1: LocationCoords,
    coord2: LocationCoords
  ): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(coord2.latitude - coord1.latitude);
    const dLon = this.toRadians(coord2.longitude - coord1.longitude);
    
    const a =
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(coord1.latitude)) *
        Math.cos(this.toRadians(coord2.latitude)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;
    
    return Math.round(distance * 100) / 100; // Round to 2 decimal places
  }

  /**
   * Convert degrees to radians
   */
  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  /**
   * Watch location changes (for real-time updates)
   */
  async watchLocation(
    callback: (location: LocationCoords) => void,
    errorCallback?: (error: Error) => void
  ): Promise<Location.LocationSubscription | null> {
    try {
      const permission = await this.checkPermission();
      
      if (!permission.granted) {
        console.warn('Location permission not granted for watching');
        return null;
      }

      const subscription = await Location.watchPositionAsync(
        {
          accuracy: Location.Accuracy.Balanced,
          timeInterval: 30000, // Update every 30 seconds
          distanceInterval: 100, // Update when moved 100 meters
        },
        (location) => {
          const coords = {
            latitude: location.coords.latitude,
            longitude: location.coords.longitude,
          };
          this.currentLocation = coords;
          callback(coords);
        }
      );

      return subscription;
    } catch (error) {
      console.error('Error watching location:', error);
      if (errorCallback) {
        errorCallback(error as Error);
      }
      return null;
    }
  }

  /**
   * Get location permission status without requesting
   */
  getPermissionStatus(): LocationPermissionStatus | null {
    return this.permissionStatus;
  }

  /**
   * Clear cached location data
   */
  clearCache(): void {
    this.currentLocation = null;
    this.permissionStatus = null;
  }

  /**
   * Check if location services are enabled on the device
   */
  async isLocationServicesEnabled(): Promise<boolean> {
    try {
      return await Location.hasServicesEnabledAsync();
    } catch (error) {
      console.error('Error checking location services:', error);
      return false;
    }
  }

  /**
   * Get address from coordinates (reverse geocoding)
   */
  async getAddressFromCoords(coords: LocationCoords): Promise<string | null> {
    try {
      const permission = await this.checkPermission();

      if (!permission.granted) {
        console.warn('Location permission not granted for reverse geocoding');
        return null;
      }

      const addresses = await Location.reverseGeocodeAsync(coords);

      if (addresses.length > 0) {
        const address = addresses[0];
        const parts = [
          address.street,
          address.city,
          address.region,
          address.country,
        ].filter(Boolean);

        return parts.join(', ');
      }

      return null;
    } catch (error) {
      console.error('Error getting address from coordinates:', error);
      return null;
    }
  }

  /**
   * Get detailed address object from coordinates
   */
  async getDetailedAddressFromCoords(coords: LocationCoords): Promise<{
    street?: string;
    city?: string;
    state?: string;
    country?: string;
    postalCode?: string;
  } | null> {
    try {
      const permission = await this.checkPermission();

      if (!permission.granted) {
        console.warn('Location permission not granted for reverse geocoding');
        return null;
      }

      const addresses = await Location.reverseGeocodeAsync(coords);

      if (addresses.length > 0) {
        const address = addresses[0];
        return {
          street: address.street || address.name || '',
          city: address.city || address.district || '',
          state: address.region || address.subregion || '',
          country: address.country || 'India',
          postalCode: address.postalCode || '',
        };
      }

      return null;
    } catch (error) {
      console.error('Error getting detailed address from coordinates:', error);
      return null;
    }
  }
}

// Export singleton instance
export const locationService = new LocationService();
