import { platformConfig } from '../utils/platformConfig';

export interface AppSettings {
  application_name: string;
  app_version: string;
  logo: string | null;
  favicon: string | null;
  app_logo: string | null;
}

export interface SettingsResponse {
  success: boolean;
  data: AppSettings;
  message: string;
}

class SettingsService {
  private baseUrl: string;
  private fallbackUrls: string[];
  private cachedSettings: AppSettings | null = null;

  constructor() {
    // Use platform-specific configuration
    this.baseUrl = platformConfig.getBaseURL();
    this.fallbackUrls = platformConfig.getFallbackURLs();
  }

  /**
   * Test connectivity and switch to working URL if needed
   */
  async testAndSwitchToWorkingUrl(): Promise<boolean> {
    const urlsToTest = [this.baseUrl, ...this.fallbackUrls];

    for (const baseUrl of urlsToTest) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);

        const response = await fetch(`${baseUrl}/settings/public`, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          },
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (response.ok) {
          if (baseUrl !== this.baseUrl) {
            this.baseUrl = baseUrl;
          }

          return true;
        }
      } catch (error) {
        // Silent fail for connectivity tests
      }
    }

    return false;
  }

  /**
   * Get public app settings with retry logic
   */
  async getPublicSettings(): Promise<AppSettings | null> {
    try {
      // Return cached settings if available
      if (this.cachedSettings) {
        return this.cachedSettings;
      }

      // Test connectivity and switch to working URL if needed
      await this.testAndSwitchToWorkingUrl();

      // Try multiple times with different approaches
      for (let attempt = 1; attempt <= 3; attempt++) {
        try {

          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 10000);

          const response = await fetch(`${this.baseUrl}/settings/public`, {
            method: 'GET',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
            },
            signal: controller.signal,
          });

          clearTimeout(timeoutId);

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const data: SettingsResponse = await response.json();

          if (data.success) {
            // Process logo URLs to be absolute
            const storageBaseUrl = platformConfig.getStorageURL().replace('/storage', '');
            if (data.data.app_logo && !data.data.app_logo.startsWith('http')) {
              data.data.app_logo = `${storageBaseUrl}${data.data.app_logo}`;
            }
            if (data.data.logo && !data.data.logo.startsWith('http')) {
              data.data.logo = `${storageBaseUrl}${data.data.logo}`;
            }

            this.cachedSettings = data.data;
            return data.data;
          } else {
            throw new Error(data.message || 'Failed to fetch settings');
          }
        } catch (error) {
          if (attempt === 3) {
            throw error;
          }
          // Wait before retry
          await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
      }
    } catch (error) {
      console.error('❌ All settings fetch attempts failed:', error);

      // Return fallback settings
      const fallbackSettings: AppSettings = {
        application_name: 'Waitinglist App',
        app_version: '1.0.0',
        logo: null,
        favicon: null,
        app_logo: null,
      };


      return fallbackSettings;
    }

    return null;
  }

  /**
   * Get app logo URL
   */
  async getAppLogo(): Promise<string | null> {
    const settings = await this.getPublicSettings();
    return settings?.app_logo || null;
  }

  /**
   * Get app name
   */
  async getAppName(): Promise<string> {
    const settings = await this.getPublicSettings();
    return settings?.application_name || 'Waitinglist App';
  }

  /**
   * Get app version
   */
  async getAppVersion(): Promise<string> {
    const settings = await this.getPublicSettings();
    return settings?.app_version || '1.0.0';
  }

  /**
   * Clear cached settings (useful for refreshing)
   */
  clearCache(): void {
    this.cachedSettings = null;
  }

  /**
   * Update base URL (useful for different environments)
   */
  setBaseUrl(url: string): void {
    this.baseUrl = url;
    this.clearCache(); // Clear cache when URL changes
  }

  /**
   * Get current base URL
   */
  getBaseUrl(): string {
    return this.baseUrl;
  }
}

// Export singleton instance
export const settingsService = new SettingsService();
