import NetInfo from '@react-native-community/netinfo';

export interface NetworkStatus {
  isConnected: boolean;
  type: string;
  isInternetReachable: boolean | null;
}

class NetworkService {
  private listeners: ((status: NetworkStatus) => void)[] = [];

  /**
   * Check current network status
   */
  async checkNetworkStatus(): Promise<NetworkStatus> {
    try {
      const state = await NetInfo.fetch();
      return {
        isConnected: state.isConnected ?? false,
        type: state.type,
        isInternetReachable: state.isInternetReachable,
      };
    } catch (error) {
      console.error('Error checking network status:', error);
      return {
        isConnected: false,
        type: 'unknown',
        isInternetReachable: false,
      };
    }
  }

  /**
   * Test connectivity to our backend server
   */
  async testBackendConnectivity(): Promise<boolean> {
    const testUrls = [
      'http://********:8000/api/settings/public',      // Android emulator
      'http://***********:8000/api/settings/public',   // Host machine IP
      'http://************:8000/api/settings/public',  // Previous IP
      'http://localhost:8000/api/settings/public',
      'http://127.0.0.1:8000/api/settings/public',
    ];

    for (const url of testUrls) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);

        const response = await fetch(url, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          },
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (response.ok) {
          const data = await response.json();
          return true;
        }
      } catch (error) {
        // Silent fail for connectivity tests
      }
    }

    return false;
  }

  /**
   * Get detailed network diagnostics
   */
  async getNetworkDiagnostics(): Promise<{
    networkStatus: NetworkStatus;
    backendReachable: boolean;
    timestamp: string;
  }> {
    const networkStatus = await this.checkNetworkStatus();
    const backendReachable = await this.testBackendConnectivity();
    
    const diagnostics = {
      networkStatus,
      backendReachable,
      timestamp: new Date().toISOString(),
    };

    return diagnostics;
  }

  /**
   * Subscribe to network status changes
   */
  subscribe(callback: (status: NetworkStatus) => void): () => void {
    this.listeners.push(callback);

    const unsubscribe = NetInfo.addEventListener(state => {
      const status: NetworkStatus = {
        isConnected: state.isConnected ?? false,
        type: state.type,
        isInternetReachable: state.isInternetReachable,
      };
      callback(status);
    });

    return () => {
      this.listeners = this.listeners.filter(listener => listener !== callback);
      unsubscribe();
    };
  }

  /**
   * Wait for network connection
   */
  async waitForConnection(timeoutMs: number = 10000): Promise<boolean> {
    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        resolve(false);
      }, timeoutMs);

      const unsubscribe = this.subscribe((status) => {
        if (status.isConnected) {
          clearTimeout(timeout);
          unsubscribe();
          resolve(true);
        }
      });

      // Check immediately
      this.checkNetworkStatus().then((status) => {
        if (status.isConnected) {
          clearTimeout(timeout);
          unsubscribe();
          resolve(true);
        }
      });
    });
  }
}

// Export singleton instance
export const networkService = new NetworkService();
