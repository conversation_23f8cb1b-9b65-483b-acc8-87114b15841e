import { GOOGLE_PLACES_CONFIG } from '../config/googlePlaces';

export interface PlaceDetails {
  place_id: string;
  description: string;
  structured_formatting: {
    main_text: string;
    secondary_text: string;
  };
  geometry?: {
    location: {
      lat: number;
      lng: number;
    };
  };
}

export interface LocationCoords {
  latitude: number;
  longitude: number;
}

class GooglePlacesService {
  private apiKey: string;
  private baseUrl = 'https://maps.googleapis.com/maps/api/place';

  constructor() {
    this.apiKey = GOOGLE_PLACES_CONFIG.API_KEY;
  }

  /**
   * Search for places using Google Places Autocomplete API
   */
  async searchPlaces(query: string, types: string = '(cities)'): Promise<PlaceDetails[]> {
    if (!query.trim() || this.apiKey === 'YOUR_GOOGLE_PLACES_API_KEY') {
      // Fallback to hardcoded cities if no API key is configured
      return this.getHardcodedCities(query);
    }

    try {
      const url = `${this.baseUrl}/autocomplete/json`;
      const params = new URLSearchParams({
        input: query,
        types: types,
        language: GOOGLE_PLACES_CONFIG.DEFAULT_QUERY.language,
        key: this.apiKey,
      });

      const response = await fetch(`${url}?${params}`);
      const data = await response.json();

      if (data.status === 'OK') {
        return data.predictions || [];
      } else {
        console.warn('Google Places API error:', data.status, data.error_message);
        return this.getHardcodedCities(query);
      }
    } catch (error) {
      console.error('Error searching places:', error);
      return this.getHardcodedCities(query);
    }
  }

  /**
   * Get place details including coordinates
   */
  async getPlaceDetails(placeId: string): Promise<LocationCoords | null> {
    if (this.apiKey === 'YOUR_GOOGLE_PLACES_API_KEY') {
      return null;
    }

    try {
      const url = `${this.baseUrl}/details/json`;
      const params = new URLSearchParams({
        place_id: placeId,
        fields: 'geometry',
        key: this.apiKey,
      });

      const response = await fetch(`${url}?${params}`);
      const data = await response.json();

      if (data.status === 'OK' && data.result?.geometry?.location) {
        return {
          latitude: data.result.geometry.location.lat,
          longitude: data.result.geometry.location.lng,
        };
      }
    } catch (error) {
      console.error('Error getting place details:', error);
    }

    return null;
  }

  /**
   * Fallback to hardcoded cities when Google Places API is not available
   */
  private getHardcodedCities(query: string): PlaceDetails[] {
    const cities = [
      'Mumbai, Maharashtra',
      'Delhi, Delhi',
      'Bangalore, Karnataka',
      'Chennai, Tamil Nadu',
      'Hyderabad, Telangana',
      'Pune, Maharashtra',
      'Kolkata, West Bengal',
      'Ahmedabad, Gujarat',
      'Jaipur, Rajasthan',
      'Lucknow, Uttar Pradesh',
      'Surat, Gujarat',
      'Kanpur, Uttar Pradesh',
      'Nagpur, Maharashtra',
      'Indore, Madhya Pradesh',
      'Thane, Maharashtra',
      'Bhopal, Madhya Pradesh',
      'Visakhapatnam, Andhra Pradesh',
      'Pimpri-Chinchwad, Maharashtra',
      'Patna, Bihar',
      'Vadodara, Gujarat',
      'Rajkot, Gujarat',
      'Gandhinagar, Gujarat',
      'Bhavnagar, Gujarat',
      'Junagadh, Gujarat',
      'Anand, Gujarat',
      'Morbi, Gujarat',
      'Nadiad, Gujarat',
      'Surendranagar, Gujarat',
      'Bharuch, Gujarat',
      'Vapi, Gujarat',
      'Manali, Himachal Pradesh',
      'Shimla, Himachal Pradesh',
      'Goa, Goa',
      'Udaipur, Rajasthan',
      'Agra, Uttar Pradesh',
      'Varanasi, Uttar Pradesh',
      'Rishikesh, Uttarakhand',
      'Haridwar, Uttarakhand',
      'Dharamshala, Himachal Pradesh',
      'Kasol, Himachal Pradesh',
      'Kochi, Kerala',
      'Thiruvananthapuram, Kerala',
      'Kozhikode, Kerala',
      'Thrissur, Kerala',
      'Mysore, Karnataka',
      'Hubli, Karnataka',
      'Mangalore, Karnataka',
      'Belgaum, Karnataka',
      'Coimbatore, Tamil Nadu',
      'Madurai, Tamil Nadu',
      'Tiruchirappalli, Tamil Nadu',
      'Salem, Tamil Nadu',
      'Tirunelveli, Tamil Nadu',
      'Erode, Tamil Nadu',
      'Vellore, Tamil Nadu',
      'Thoothukudi, Tamil Nadu',
    ];

    const filteredCities = query.trim()
      ? cities.filter(city =>
          city.toLowerCase().includes(query.toLowerCase())
        )
      : cities;

    return filteredCities.map((city, index) => ({
      place_id: `hardcoded_${index}`,
      description: city,
      structured_formatting: {
        main_text: city.split(', ')[0],
        secondary_text: city.split(', ')[1] || '',
      },
    }));
  }

  /**
   * Check if Google Places API is configured
   */
  isConfigured(): boolean {
    return this.apiKey !== 'YOUR_GOOGLE_PLACES_API_KEY';
  }
}

// Export singleton instance
export const googlePlacesService = new GooglePlacesService();
