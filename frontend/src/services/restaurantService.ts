import { LocationCoords } from './locationService';

export interface Restaurant {
  id: number;
  name: string;
  location: string;
  full_address: string;
  address_line_1: string | null;
  address_line_2: string | null;
  city: string | null;
  state: string | null;
  country: string | null;
  postal_code: string | null;
  contact_number: string;
  profile: string | null;
  current_waiting_count: number;
  distance: number | null;
  owner_name: string;
  description: string | null;
  is_active: boolean;
}

export interface RestaurantListResponse {
  success: boolean;
  data: {
    data: Restaurant[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
  message: string;
}

export interface RestaurantSearchParams {
  search?: string;
  latitude?: number;
  longitude?: number;
  radius?: number;
  page?: number;
}

class RestaurantService {
  private baseUrl: string;
  private fallbackUrls: string[];

  constructor() {
    // Primary URL - for Android emulator use ********
    this.baseUrl = 'http://********:8000/api';

    // Fallback URLs for different network configurations
    this.fallbackUrls = [
      'http://***********:8000/api',   // Host machine IP
      'http://************:8000/api',  // Previous IP
      'http://localhost:8000/api',
      'http://127.0.0.1:8000/api',
    ];
  }

  /**
   * Test connectivity and switch to working URL if needed
   */
  async testAndSwitchToWorkingUrl(): Promise<boolean> {
    const urlsToTest = [this.baseUrl, ...this.fallbackUrls];

    for (const baseUrl of urlsToTest) {
      try {
        console.log(`🔍 Testing restaurant service connectivity to: ${baseUrl}`);

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000);

        const response = await fetch(`${baseUrl}/settings/public`, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          },
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (response.ok) {
          console.log(`✅ Restaurant service connectivity test passed for: ${baseUrl}`);

          if (baseUrl !== this.baseUrl) {
            console.log(`🔄 Switching restaurant service base URL from ${this.baseUrl} to ${baseUrl}`);
            this.baseUrl = baseUrl;
          }

          return true;
        }
      } catch (error) {
        console.log(`❌ Restaurant service connectivity test failed for ${baseUrl}:`, error);
      }
    }

    console.log('❌ All restaurant service connectivity tests failed');
    return false;
  }

  /**
   * Get public restaurants list (no authentication required)
   */
  async getPublicRestaurants(params: RestaurantSearchParams = {}): Promise<RestaurantListResponse> {
    try {
      // Test connectivity and switch to working URL if needed
      await this.testAndSwitchToWorkingUrl();

      const searchParams = new URLSearchParams();

      if (params.search?.trim()) {
        searchParams.append('search', params.search.trim());
      }

      if (params.latitude !== undefined && params.longitude !== undefined) {
        searchParams.append('latitude', params.latitude.toString());
        searchParams.append('longitude', params.longitude.toString());
        
        if (params.radius) {
          searchParams.append('radius', params.radius.toString());
        }
      }

      if (params.page) {
        searchParams.append('page', params.page.toString());
      }

      const url = `${this.baseUrl}/restaurants/public?${searchParams.toString()}`;
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error fetching public restaurants:', error);
      throw error;
    }
  }

  /**
   * Search restaurants by name or location
   */
  async searchRestaurants(
    query: string,
    userLocation?: LocationCoords,
    radius: number = 50
  ): Promise<RestaurantListResponse> {
    const params: RestaurantSearchParams = {
      search: query,
      radius,
    };

    if (userLocation) {
      params.latitude = userLocation.latitude;
      params.longitude = userLocation.longitude;
    }

    return this.getPublicRestaurants(params);
  }

  /**
   * Get nearby restaurants based on user location
   */
  async getNearbyRestaurants(
    userLocation: LocationCoords,
    radius: number = 10
  ): Promise<RestaurantListResponse> {
    const params: RestaurantSearchParams = {
      latitude: userLocation.latitude,
      longitude: userLocation.longitude,
      radius,
    };

    return this.getPublicRestaurants(params);
  }

  /**
   * Get all restaurants (without location filtering)
   */
  async getAllRestaurants(page: number = 1): Promise<RestaurantListResponse> {
    const params: RestaurantSearchParams = {
      page,
    };

    return this.getPublicRestaurants(params);
  }

  /**
   * Get restaurant by ID (if needed for detailed view)
   */
  async getRestaurantById(id: number): Promise<Restaurant | null> {
    try {
      // This would require a specific endpoint for single restaurant
      // For now, we'll get all restaurants and filter
      const response = await this.getAllRestaurants();
      
      if (response.success) {
        const restaurant = response.data.data.find(r => r.id === id);
        return restaurant || null;
      }
      
      return null;
    } catch (error) {
      console.error('Error fetching restaurant by ID:', error);
      return null;
    }
  }

  /**
   * Update base URL (useful for different environments)
   */
  setBaseUrl(url: string): void {
    this.baseUrl = url;
  }

  /**
   * Get current base URL
   */
  getBaseUrl(): string {
    return this.baseUrl;
  }

  /**
   * Format restaurant profile image URL
   */
  getImageUrl(imagePath: string | null): string | null {
    if (!imagePath) return null;
    
    // If it's already a full URL, return as is
    if (imagePath.startsWith('http')) {
      return imagePath;
    }
    
    // Otherwise, construct the full URL
    return `${this.baseUrl.replace('/api', '')}/storage/${imagePath}`;
  }

  /**
   * Validate restaurant data
   */
  validateRestaurant(restaurant: any): restaurant is Restaurant {
    return (
      typeof restaurant.id === 'number' &&
      typeof restaurant.name === 'string' &&
      typeof restaurant.location === 'string' &&
      typeof restaurant.contact_number === 'string' &&
      typeof restaurant.current_waiting_count === 'number' &&
      typeof restaurant.owner_name === 'string' &&
      (restaurant.profile === null || typeof restaurant.profile === 'string') &&
      (restaurant.distance === null || typeof restaurant.distance === 'number')
    );
  }

  /**
   * Process restaurant list response and validate data
   */
  processRestaurantList(response: any): Restaurant[] {
    if (!response.success || !response.data?.data) {
      return [];
    }

    return response.data.data
      .filter((restaurant: any) => this.validateRestaurant(restaurant))
      .map((restaurant: any) => ({
        ...restaurant,
        profile: this.getImageUrl(restaurant.profile),
      }));
  }

  /**
   * Get restaurants with error handling and data processing
   */
  async getRestaurantsWithProcessing(params: RestaurantSearchParams = {}): Promise<{
    restaurants: Restaurant[];
    pagination: {
      currentPage: number;
      lastPage: number;
      perPage: number;
      total: number;
    } | null;
    error: string | null;
  }> {
    try {
      const response = await this.getPublicRestaurants(params);
      
      if (!response.success) {
        return {
          restaurants: [],
          pagination: null,
          error: response.message || 'Failed to fetch restaurants',
        };
      }

      const restaurants = this.processRestaurantList(response);
      const pagination = response.data ? {
        currentPage: response.data.current_page,
        lastPage: response.data.last_page,
        perPage: response.data.per_page,
        total: response.data.total,
      } : null;

      return {
        restaurants,
        pagination,
        error: null,
      };
    } catch (error) {
      console.error('Error in getRestaurantsWithProcessing:', error);
      return {
        restaurants: [],
        pagination: null,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }
}

// Export singleton instance
export const restaurantService = new RestaurantService();
