import { useState, useEffect, useCallback } from 'react';
import { apiService } from '../services/api';

interface UseRestaurantStatusReturn {
  isOpen: boolean;
  loading: boolean;
  waitingCount: number;
  toggleStatus: () => Promise<void>;
  refreshStatus: () => Promise<void>;
  checkAndAutoClose: (currentWaitingCount: number) => void;
}

export function useRestaurantStatus(): UseRestaurantStatusReturn {
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [waitingCount, setWaitingCount] = useState(0);

  // Load restaurant status
  const loadRestaurantStatus = useCallback(async () => {
    try {
      setLoading(true);
      const response = await apiService.getMyRestaurant();

      if (response.success && response.data) {
        setIsOpen(response.data.is_active || false);
        setWaitingCount(response.data.current_waiting_count || 0);
      }
    } catch (error) {
      console.error('Failed to load restaurant status:', error);
      // Default to closed if we can't load status
      setIsOpen(false);
      setWaitingCount(0);
    } finally {
      setLoading(false);
    }
  }, []);

  // Toggle restaurant status
  const toggleStatus = useCallback(async () => {
    try {
      const response = await apiService.toggleMyRestaurantStatus();
      
      if (response.success && response.data) {
        setIsOpen(response.data.is_active);
      }
    } catch (error) {
      console.error('Failed to toggle restaurant status:', error);
      throw error;
    }
  }, []);

  // Auto-close restaurant when waiting count reaches 0
  const checkAndAutoClose = useCallback((currentWaitingCount: number) => {
    setWaitingCount(currentWaitingCount);

    // Auto-close if waiting count is 0 and restaurant is currently open
    if (currentWaitingCount === 0 && isOpen) {
      console.log('🔄 Auto-closing restaurant: waiting count reached 0');
      setIsOpen(false);
      // Also update on server - but only call the API, don't use toggleStatus to avoid infinite loop
      apiService.toggleMyRestaurantStatus().catch(error => {
        console.error('Failed to auto-close restaurant:', error);
        // Revert local state if server update fails
        setIsOpen(true);
      });
    }
  }, [isOpen]);

  // Refresh status
  const refreshStatus = useCallback(async () => {
    await loadRestaurantStatus();
  }, [loadRestaurantStatus]);

  // Load initial status
  useEffect(() => {
    loadRestaurantStatus();
  }, [loadRestaurantStatus]);

  return {
    isOpen,
    loading,
    waitingCount,
    toggleStatus,
    refreshStatus,
    checkAndAutoClose,
  };
}
