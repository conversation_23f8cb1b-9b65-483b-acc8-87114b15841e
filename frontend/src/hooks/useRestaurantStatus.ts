import { useState, useEffect, useCallback } from 'react';
import { apiService } from '../services/api';

interface UseRestaurantStatusReturn {
  isOpen: boolean;
  loading: boolean;
  toggleStatus: () => Promise<void>;
  refreshStatus: () => Promise<void>;
}

export function useRestaurantStatus(): UseRestaurantStatusReturn {
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(true);

  // Load restaurant status
  const loadRestaurantStatus = useCallback(async () => {
    try {
      setLoading(true);
      const response = await apiService.getMyRestaurant();
      
      if (response.success && response.data) {
        setIsOpen(response.data.is_active || false);
      }
    } catch (error) {
      console.error('Failed to load restaurant status:', error);
      // Default to closed if we can't load status
      setIsOpen(false);
    } finally {
      setLoading(false);
    }
  }, []);

  // Toggle restaurant status
  const toggleStatus = useCallback(async () => {
    try {
      const response = await apiService.toggleMyRestaurantStatus();
      
      if (response.success && response.data) {
        setIsOpen(response.data.is_active);
      }
    } catch (error) {
      console.error('Failed to toggle restaurant status:', error);
      throw error;
    }
  }, []);

  // Refresh status
  const refreshStatus = useCallback(async () => {
    await loadRestaurantStatus();
  }, [loadRestaurantStatus]);

  // Load initial status
  useEffect(() => {
    loadRestaurantStatus();
  }, [loadRestaurantStatus]);

  return {
    isOpen,
    loading,
    toggleStatus,
    refreshStatus,
  };
}
