<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .loading {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>API Connection Test</h1>
    
    <div>
        <button onclick="testSimpleAPI()">Test Simple API</button>
        <button onclick="testRestaurantsAPI()">Test Restaurants API</button>
        <button onclick="testSettingsAPI()">Test Settings API</button>
    </div>
    
    <div id="results"></div>

    <script>
        const API_BASE = 'http://************:8000/api';
        
        function addResult(message, type = 'loading') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.innerHTML = message;
            resultsDiv.appendChild(resultDiv);
            return resultDiv;
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        async function testAPI(endpoint, description) {
            const resultDiv = addResult(`Testing ${description}...`, 'loading');
            
            try {
                const response = await fetch(`${API_BASE}${endpoint}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                    },
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'test-result success';
                    resultDiv.innerHTML = `
                        <strong>✅ ${description} - SUCCESS</strong><br>
                        Status: ${response.status}<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = `
                        <strong>❌ ${description} - FAILED</strong><br>
                        Status: ${response.status}<br>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `
                    <strong>❌ ${description} - ERROR</strong><br>
                    Error: ${error.message}<br>
                    <pre>${error.stack}</pre>
                `;
            }
        }
        
        function testSimpleAPI() {
            clearResults();
            testAPI('/test', 'Simple API Test');
        }
        
        function testRestaurantsAPI() {
            clearResults();
            testAPI('/restaurants/public', 'Restaurants API');
        }
        
        function testSettingsAPI() {
            clearResults();
            testAPI('/settings/public', 'Settings API');
        }
        
        // Auto-run simple test on page load
        window.onload = function() {
            testSimpleAPI();
        };
    </script>
</body>
</html>
