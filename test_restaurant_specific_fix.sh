#!/bin/bash

# Test script for restaurant-specific update fix

echo "🎯 Testing Restaurant-Specific Update Fix"
echo "========================================="
echo ""

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print test results
print_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

# Function to print test section
print_section() {
    echo -e "${YELLOW}📋 $1${NC}"
    echo "----------------------------------------"
}

# Test 1: Check intelligent sync implementation
print_section "Intelligent Sync Implementation Check"

# Check if change detection is implemented
if grep -q "hasRestaurantDataChanged" ../frontend/src/services/sync.ts; then
    print_result 0 "Restaurant change detection implemented"
else
    print_result 1 "Restaurant change detection missing"
fi

# Check if lastRestaurantData tracking exists
if grep -q "lastRestaurantData" ../frontend/src/services/sync.ts; then
    print_result 0 "Restaurant data tracking implemented"
else
    print_result 1 "Restaurant data tracking missing"
fi

# Check if conditional notification exists
if grep -q "No restaurant data changes - skipping real-time notification" ../frontend/src/services/sync.ts; then
    print_result 0 "Conditional notification logic implemented"
else
    print_result 1 "Conditional notification logic missing"
fi

echo ""

# Test 2: Check backend specificity
print_section "Backend Restaurant Specificity Check"

# Verify backend only updates specific restaurant
if grep -A 5 "updateRestaurantWaitingCount" ../backend/app/Http/Controllers/Api/RestaurantUserController.php | grep -q "where('owner_id', \$ownerId)"; then
    print_result 0 "Backend correctly updates only specific restaurant"
else
    print_result 1 "Backend may be updating all restaurants"
fi

echo ""

# Test 3: Get current restaurant data for testing
print_section "Current Restaurant Data Analysis"

echo "🔍 Fetching current restaurant data..."
curl -s http://***********:8000/api/restaurants/public > /tmp/restaurants_current.json

if [ $? -eq 0 ]; then
    print_result 0 "Successfully fetched restaurant data"
    
    # Show current counts
    echo ""
    echo "Current restaurant waiting counts:"
    echo "=================================="
    cat /tmp/restaurants_current.json | grep -o '"name":"[^"]*","current_waiting_count":[0-9]*' | sed 's/"name":"//g' | sed 's/","current_waiting_count":/ → /g' | head -5
    
    # Count total restaurants
    total_restaurants=$(cat /tmp/restaurants_current.json | grep -o '"current_waiting_count":[0-9]*' | wc -l)
    echo ""
    echo "Total restaurants: $total_restaurants"
else
    print_result 1 "Failed to fetch restaurant data"
    exit 1
fi

echo ""

# Test 4: Frontend build check
print_section "Frontend Build Check"

build_result=$(npx expo export --platform android --quiet 2>&1)
if [ $? -eq 0 ]; then
    print_result 0 "Frontend builds successfully with intelligent sync"
else
    print_result 1 "Frontend build failed"
fi

echo ""

# Test 5: Expected behavior explanation
print_section "Expected Behavior After Fix"

echo -e "${BLUE}🎯 How the intelligent sync works:${NC}"
echo ""
echo "1. 📊 User edits a user in Restaurant A"
echo "2. 🔄 Backend updates ONLY Restaurant A's count"
echo "3. 📡 Frontend sync fetches all restaurant data"
echo "4. 🧠 Sync service compares new vs old data"
echo "5. ✅ Only notifies listeners if Restaurant A's count changed"
echo "6. 🎨 Only Restaurant A visually updates"
echo ""

echo -e "${BLUE}🔍 Console messages you'll see:${NC}"
echo ""
echo "When Restaurant A's count changes:"
echo "• 'Restaurant [Name] count changed: X → Y'"
echo "• 'Restaurant data changed - triggering real-time listeners'"
echo ""
echo "When no restaurants change:"
echo "• 'No restaurant data changes - skipping real-time notification'"
echo ""

echo -e "${BLUE}📱 Visual behavior:${NC}"
echo ""
echo "Before fix:"
echo "❌ Edit user in Restaurant A → ALL restaurants refresh visually"
echo ""
echo "After fix:"
echo "✅ Edit user in Restaurant A → ONLY Restaurant A refreshes visually"
echo "✅ Restaurant B, C, D remain completely unchanged"
echo ""

# Test 6: Specific test case
print_section "Specific Test Case"

echo -e "${BLUE}🧪 Test this exact scenario:${NC}"
echo ""
echo "1. 📊 Initial state:"
echo "   • Restaurant A: 10 people waiting"
echo "   • Restaurant B: 5 people waiting"
echo "   • Restaurant C: 8 people waiting"
echo ""
echo "2. ✏️ Action:"
echo "   • Edit a user in Restaurant A"
echo "   • Change party size from 4 to 6 (+2 change)"
echo ""
echo "3. ✅ Expected result:"
echo "   • Restaurant A: 12 people waiting (10-4+6=12)"
echo "   • Restaurant B: 5 people waiting (UNCHANGED)"
echo "   • Restaurant C: 8 people waiting (UNCHANGED)"
echo ""
echo "4. 🔍 Visual verification:"
echo "   • Only Restaurant A should show visual update"
echo "   • Restaurant B and C should not flash/refresh"
echo "   • Console should show: 'Restaurant A count changed: 10 → 12'"
echo ""

# Test 7: Manual testing instructions
print_section "Manual Testing Instructions"

echo -e "${BLUE}🧪 Step-by-step testing:${NC}"
echo ""
echo "1. 🚀 Start the system:"
echo "   ./start_clean_system.sh"
echo ""
echo "2. 📱 Open restaurant list and note counts:"
echo "   • Take screenshot or write down each restaurant's count"
echo ""
echo "3. ✏️ Edit a user in ONE specific restaurant:"
echo "   • Go to waiting list for Restaurant A"
echo "   • Edit any user (change party size)"
echo "   • Save the changes"
echo ""
echo "4. 🔍 Check console logs:"
echo "   • Should see: 'Restaurant [Name] count changed: X → Y'"
echo "   • Should see: 'Restaurant data changed - triggering real-time listeners'"
echo ""
echo "5. ✅ Verify visual behavior:"
echo "   • Only the edited restaurant should show updated count"
echo "   • Other restaurants should remain visually unchanged"
echo "   • No unnecessary 'flashing' of other restaurants"
echo ""
echo "6. 🔄 Test with no changes:"
echo "   • Wait 5 seconds for auto-refresh"
echo "   • Should see: 'No restaurant data changes - skipping real-time notification'"
echo ""

# Test 8: Troubleshooting
print_section "Troubleshooting"

echo -e "${BLUE}🔧 If the issue persists:${NC}"
echo ""
echo "1. Check console logs for:"
echo "   • 'Initialized restaurant data for change detection'"
echo "   • 'Restaurant [Name] count changed: X → Y'"
echo "   • 'Restaurant data changed - triggering real-time listeners'"
echo ""
echo "2. Verify backend behavior:"
echo "   • Check if only one restaurant's count actually changes in database"
echo "   • Use API calls to verify specific restaurant updates"
echo ""
echo "3. Clear cache and restart:"
echo "   • Stop all servers"
echo "   • Clear browser cache"
echo "   • Restart with ./start_clean_system.sh"
echo ""

echo -e "${GREEN}🎊 Expected outcome: Only the specific restaurant you edit will show visual updates!${NC}"
echo ""
echo "Start testing: ./start_clean_system.sh"
