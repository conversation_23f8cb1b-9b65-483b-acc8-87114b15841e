#!/bin/bash

# Script to start both backend and frontend for the waiting list app

echo "🚀 Starting Waiting List App..."
echo ""

# Function to check if a port is in use
check_port() {
    lsof -ti:$1 > /dev/null 2>&1
}

# Function to start backend
start_backend() {
    echo "📡 Starting Laravel Backend..."
    cd backend
    
    # Check if port 8000 is already in use
    if check_port 8000; then
        echo "✅ Backend already running on port 8000"
    else
        echo "🔧 Running migrations..."
        php artisan migrate --force
        
        echo "📊 Updating waiting counts..."
        php artisan waiting:update-counts
        
        echo "🌐 Starting Laravel server..."
        php artisan serve --host=0.0.0.0 --port=8000 &
        BACKEND_PID=$!
        echo "✅ Backend started with PID: $BACKEND_PID"
    fi
    
    cd ..
}

# Function to start frontend
start_frontend() {
    echo ""
    echo "📱 Starting React Native Frontend..."
    cd frontend
    
    # Check if port 8081 is already in use
    if check_port 8081; then
        echo "✅ Frontend already running on port 8081"
    else
        echo "📦 Installing dependencies..."
        npm install
        
        echo "🚀 Starting Expo development server..."
        npx expo start &
        FRONTEND_PID=$!
        echo "✅ Frontend started with PID: $FRONTEND_PID"
    fi
    
    cd ..
}

# Function to show connection info
show_info() {
    echo ""
    echo "🎉 App is ready!"
    echo ""
    echo "📡 Backend API: http://***********:8000/api"
    echo "📱 Frontend: http://localhost:8081"
    echo ""
    echo "📋 Available API endpoints:"
    echo "  • GET  /api/restaurants/public - Public restaurant list"
    echo "  • POST /api/restaurant-users/{id}/mark-dine-in - Mark as dine-in"
    echo "  • POST /api/restaurant-users/{id}/mark-waiting - Mark as waiting"
    echo "  • GET  /api/restaurant-users/waiting-count - Get waiting count"
    echo ""
    echo "🔧 To test the API:"
    echo "  curl http://***********:8000/api/restaurants/public"
    echo ""
    echo "📱 To connect your device:"
    echo "  1. Install Expo Go app on your phone"
    echo "  2. Scan the QR code that appears"
    echo "  3. Or press 'a' for Android emulator, 'i' for iOS simulator"
    echo ""
    echo "⚡ Real-time features:"
    echo "  • Automatic waiting count updates every 5 seconds"
    echo "  • Dine-in checkbox with green highlighting"
    echo "  • Undo functionality for status changes"
    echo "  • Cross-device synchronization"
    echo ""
    echo "🛑 To stop the servers:"
    echo "  Press Ctrl+C or run: pkill -f 'php artisan serve'"
}

# Main execution
start_backend
start_frontend
show_info

# Keep script running
echo "Press Ctrl+C to stop all servers..."
wait
