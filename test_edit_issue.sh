#!/bin/bash

# Test script to debug the edit user issue

echo "🔧 Testing Edit User Issue"
echo "=========================="
echo ""

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print test results
print_result() {
    if [ $1 -eq 0 ]; then
        echo -e "${GREEN}✅ $2${NC}"
    else
        echo -e "${RED}❌ $2${NC}"
    fi
}

# Function to print test section
print_section() {
    echo -e "${YELLOW}📋 $1${NC}"
    echo "----------------------------------------"
}

# Test 1: Check if backend is running
print_section "Backend Status Check"

response=$(curl -s -w "%{http_code}" -o /tmp/backend_test.json http://***********:8000/api/restaurants/public)
if [ "$response" = "200" ]; then
    print_result 0 "Backend is running and responding"
else
    print_result 1 "Backend not responding (HTTP $response)"
    echo "Please start backend: cd backend && php artisan serve --host=0.0.0.0 --port=8000"
    exit 1
fi

echo ""

# Test 2: Check debugging additions
print_section "Debugging Code Check"

# Check if debugging was added to useOfflineData
if grep -q "Updating user with full object" ../frontend/src/hooks/useOfflineData.ts; then
    print_result 0 "Debugging added to useOfflineData hook"
else
    print_result 1 "Debugging missing in useOfflineData hook"
fi

# Check if debugging was added to sync service
if grep -q "updateRestaurantUserOffline called with" ../frontend/src/services/sync.ts; then
    print_result 0 "Debugging added to sync service"
else
    print_result 1 "Debugging missing in sync service"
fi

# Check if sync trigger debugging was added
if grep -q "Triggering sync after user update" ../frontend/src/hooks/useOfflineData.ts; then
    print_result 0 "Sync trigger debugging added"
else
    print_result 1 "Sync trigger debugging missing"
fi

echo ""

# Test 3: Check API endpoints
print_section "API Endpoint Testing"

# Get a sample user to test with
echo "🔍 Getting sample user data..."
users_response=$(curl -s "http://***********:8000/api/restaurant-users?per_page=1" -H "Accept: application/json")
user_id=$(echo "$users_response" | grep -o '"id":[0-9]*' | head -1 | grep -o '[0-9]*')

if [ ! -z "$user_id" ]; then
    echo "Found user ID: $user_id"
    
    # Test GET user endpoint
    get_response=$(curl -s -w "%{http_code}" -o /tmp/get_user.json "http://***********:8000/api/restaurant-users/$user_id")
    if [ "$get_response" = "200" ]; then
        print_result 0 "GET user endpoint working"
    else
        print_result 1 "GET user endpoint failed (HTTP $get_response)"
    fi
    
    # Test PUT user endpoint (without authentication for now)
    put_response=$(curl -s -w "%{http_code}" -o /tmp/put_user.json -X PUT "http://***********:8000/api/restaurant-users/$user_id" \
        -H "Content-Type: application/json" \
        -d '{"username":"Test Update","mobile_number":"1234567890","total_users_count":5}')
    
    if [ "$put_response" = "200" ] || [ "$put_response" = "401" ]; then
        print_result 0 "PUT user endpoint exists (HTTP $put_response)"
    else
        print_result 1 "PUT user endpoint failed (HTTP $put_response)"
    fi
else
    echo "⚠️  No users found to test with"
fi

echo ""

# Test 4: Check frontend build
print_section "Frontend Build Check"

cd ../frontend
build_result=$(npx expo export --platform android --quiet 2>&1)
if [ $? -eq 0 ]; then
    print_result 0 "Frontend builds successfully with debugging code"
else
    print_result 1 "Frontend build failed"
fi
cd ..

echo ""

# Test 5: Manual testing instructions
print_section "Manual Testing Instructions"

echo -e "${BLUE}🧪 To test the edit functionality:${NC}"
echo ""
echo "1. Start the app:"
echo "   ./start_clean_system.sh"
echo ""
echo "2. Navigate to waiting list and add a user"
echo ""
echo "3. Edit the user you just added"
echo ""
echo "4. Check the console logs for these messages:"
echo "   • 'Updating user with full object: {id, updateData}'"
echo "   • 'updateRestaurantUserOffline called with: {id, data}'"
echo "   • 'Triggering sync after user update...'"
echo "   • 'Sync completed after user update'"
echo ""
echo "5. Check if restaurant list updates after edit"
echo ""
echo -e "${BLUE}🔍 What to look for:${NC}"
echo ""
echo "✅ Expected behavior:"
echo "   • Console shows all debug messages"
echo "   • Restaurant list count updates within 5-10 seconds"
echo "   • Edit saves successfully"
echo ""
echo "❌ If edit doesn't trigger updates:"
echo "   • Check if 'Triggering sync after user update' appears"
echo "   • Check if API call succeeds"
echo "   • Check if sync service gets called"
echo ""
echo -e "${BLUE}🔧 Debugging tips:${NC}"
echo ""
echo "• Open browser console to see debug messages"
echo "• Compare add vs edit console output"
echo "• Check network tab for API calls"
echo "• Verify user data is being passed correctly"
echo ""

# Test 6: Compare add vs edit functionality
print_section "Add vs Edit Comparison"

echo "🔍 Checking differences between add and edit functionality..."

# Check if both call syncData
add_sync=$(grep -c "syncService.syncData" ../frontend/src/hooks/useOfflineData.ts)
echo "Number of syncData calls in useOfflineData: $add_sync"

if [ "$add_sync" -ge 4 ]; then
    print_result 0 "Both add and edit should trigger sync"
else
    print_result 1 "Missing sync triggers"
fi

# Check if edit screen calls updateUser correctly
if grep -q "await updateUser(updatedUser)" ../frontend/src/screens/EditUserScreen.tsx; then
    print_result 0 "Edit screen calls updateUser correctly"
else
    print_result 1 "Edit screen updateUser call issue"
fi

echo ""

echo -e "${GREEN}🎯 Summary:${NC}"
echo ""
echo "The debugging code has been added to help identify why edit doesn't trigger"
echo "restaurant list updates. Follow the manual testing instructions above to"
echo "see the console output and identify where the issue occurs."
echo ""
echo "Key areas to check:"
echo "1. Does edit call updateUser with correct data?"
echo "2. Does updateUser call syncService.updateRestaurantUserOffline?"
echo "3. Does updateUser call syncService.syncData()?"
echo "4. Does syncData fetch restaurant data and trigger listeners?"
echo ""
echo "Start testing with: ./start_clean_system.sh"
