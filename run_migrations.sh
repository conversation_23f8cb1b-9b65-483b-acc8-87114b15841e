#!/bin/bash

# <PERSON><PERSON>t to run the new migrations for real-time waiting list functionality

echo "Running migrations for real-time waiting list functionality..."

cd backend

# Run the new migration
php artisan migrate

# Update waiting counts for existing restaurants
php artisan waiting:update-counts

echo "Migrations completed successfully!"
echo ""
echo "New features added:"
echo "- Status field for restaurant users (waiting/dine-in)"
echo "- Real-time waiting count updates"
echo "- Dine-in checkbox and undo functionality"
echo "- Automatic count synchronization"
echo ""
echo "API endpoints added:"
echo "- POST /api/restaurant-users/{id}/mark-dine-in"
echo "- POST /api/restaurant-users/{id}/mark-waiting"
echo "- GET /api/restaurant-users/waiting-count"
echo ""
echo "To test the functionality:"
echo "1. Add users to the waiting list"
echo "2. Mark users as dine-in using the checkbox"
echo "3. Use the undo button to revert status"
echo "4. Watch real-time count updates on restaurant list"
